import{StdioServerTransport as Ct}from"@modelcontextprotocol/sdk/server/stdio.js";import{config as Bt}from"dotenv";import{resolve as Ot}from"path";import{config as lt}from"dotenv";import mt from"yargs";import{hideBin as _t}from"yargs/helpers";import{resolve as O}from"path";function H(e){let a=mt(_t(process.argv)).options({env:{type:"string",description:"Path to custom .env file to load environment variables from"},port:{type:"number",description:"Port to run the server on"}}).help().parseSync(),i;a.env?i=O(a.env):i=O(process.cwd(),".env"),lt({path:i,override:!0});let s={port:3002};return a.port?s.port=a.port:process.env.PORT&&(s.port=parseInt(process.env.PORT,10)),{...s}}import{randomUUID as ut}from"crypto";import U from"express";import{SSEServerTransport as dt}from"@modelcontextprotocol/sdk/server/sse.js";import{StreamableHTTPServerTransport as ht}from"@modelcontextprotocol/sdk/server/streamableHttp.js";import{isInitializeRequest as gt}from"@modelcontextprotocol/sdk/types.js";import"@modelcontextprotocol/sdk/server/mcp.js";var t={isHTTP:!1,log:(...e)=>{t.isHTTP?console.log("[INFO]",...e):console.error("[INFO]",...e)},error:(...e)=>{console.error("[ERROR]",...e)}};var bt=null,b={streamable:{},sse:{}};async function W(e,a){let i=U();i.use("/mcp",U.json()),i.post("/mcp",async(r,c)=>{t.log("Received StreamableHTTP request");let n=r.headers["mcp-session-id"],o;if(n&&b.streamable[n])t.log("Reusing existing StreamableHTTP transport for sessionId",n),o=b.streamable[n];else if(!n&&gt(r.body))t.log("New initialization request for StreamableHTTP sessionId",n),o=new ht({sessionIdGenerator:()=>ut(),onsessioninitialized:l=>{b.streamable[l]=o}}),o.onclose=()=>{o.sessionId&&delete b.streamable[o.sessionId]},await a.connect(o);else{t.log("Invalid request:",r.body),c.status(400).json({jsonrpc:"2.0",error:{code:-32e3,message:"Bad Request: No valid session ID provided"},id:null});return}let p=null,u=r.body.params?._meta?.progressToken,_=0;u&&(t.log(`Setting up progress notifications for token ${u} on session ${n}`),p=setInterval(async()=>{t.log("Sending progress notification",_),await a.server.notification({method:"notifications/progress",params:{progress:_,progressToken:u}}),_++},1e3)),t.log("Handling StreamableHTTP request"),await o.handleRequest(r,c,r.body),p&&clearInterval(p),t.log("StreamableHTTP request handled")});let s=async(r,c)=>{let n=r.headers["mcp-session-id"];if(!n||!b.streamable[n]){c.status(400).send("Invalid or missing session ID");return}t.log(`Received session termination request for session ${n}`);try{await b.streamable[n].handleRequest(r,c)}catch(o){console.error("Error handling session termination:",o),c.headersSent||c.status(500).send("Error processing session termination")}};i.get("/mcp",s),i.delete("/mcp",s),i.get("/sse",async(r,c)=>{t.log("Establishing new SSE connection");let n=new dt("/messages",c);t.log(`New SSE connection established for sessionId ${n.sessionId}`),t.log("/sse request headers:",r.headers),t.log("/sse request body:",r.body),b.sse[n.sessionId]=n,c.on("close",()=>{delete b.sse[n.sessionId]}),await a.connect(n)}),i.post("/messages",async(r,c)=>{let n=r.query.sessionId,o=b.sse[n];if(o)t.log(`Received SSE message for sessionId ${n}`),t.log("/messages request headers:",r.headers),t.log("/messages request body:",r.body),await o.handlePostMessage(r,c);else{c.status(400).send(`No transport found for sessionId ${n}`);return}}),bt=i.listen(e,()=>{t.log(`HTTP server listening on port ${e}`),t.log(`SSE endpoint available at http://localhost:${e}/sse`),t.log(`Message endpoint available at http://localhost:${e}/messages`),t.log(`StreamableHTTP endpoint available at http://localhost:${e}/mcp`)}),process.on("SIGINT",async()=>{t.log("Shutting down server..."),await F(b.sse),await F(b.streamable),t.log("Server shutdown complete"),process.exit(0)})}async function F(e){for(let a in e)try{await e[a]?.close(),delete e[a]}catch(i){console.error(`Error closing transport for session ${a}:`,i)}}import{McpServer as Ht}from"@modelcontextprotocol/sdk/server/mcp.js";import{z as Z}from"zod";var R=class{store={};set(a,i){this.store[a]=i}get(a){return this.store[a]}delete(a){return a in this.store?(delete this.store[a],!0):!1}has(a){return a in this.store}clear(){this.store={}}keys(){return Object.keys(this.store)}size(){return Object.keys(this.store).length}},ft=new R,w=ft;var L={"@youzan/wsc-tee-trade-common":["wsc-tee-h5","wsc"],"@youzan/wsc-tee-goods-common":["wsc-tee-h5","wsc"],"@youzan/order-domain-pc-components":["wsc-pc-trade","retail-node-order","retail-node-fulfillment"]},j={"wsc-pc-trade":"client","retail-node-order":"client","retail-node-fulfillment":"client"};import{execSync as d}from"child_process";import E from"fs";import M from"path";import{fileURLToPath as wt}from"url";import{dirname as vt}from"path";var qt=wt(import.meta.url),J=vt(qt);import yt from"node-fetch";function y(e,a={}){let s=`https://gitlab.qima-inc.com/api/v4${e}`,r=3,c=async(n=1)=>{try{let o=await yt(s,{method:"POST",headers:{"Content-Type":"application/json","PRIVATE-TOKEN":process.env.private_token},...a});if(!o.ok)throw new Error(`HTTP ${o.status}: ${o.statusText}`);return await o.json()}catch(o){if(console.error(`[GitLab API] Attempt ${n} failed for ${e}:`,o.message),n>=r)throw console.error(`[GitLab API] All ${r} attempts failed for ${e}`),o;let p=n*300;return console.log(`[GitLab API] Retrying in ${p}ms... (attempt ${n+1}/${r})`),await new Promise(u=>setTimeout(u,p)),c(n+1)}};return c()}var T=({appName:e,branchName:a,targetBranchName:i="master"})=>{let s=v(e);return y(`/projects/${s}/repository/branches?branch=${a}&ref=${i}`)},G=({appName:e,branchName:a})=>{let i=encodeURIComponent(a),s=v(e);return y(`/projects/${s}/repository/branches/${i}`,{method:"GET"})},V=({appName:e,search:a})=>{let i=v(e||"wsc");return y(`/projects/${i}/repository/branches?search=`+encodeURIComponent(a),{method:"GET"})},K=async({appName:e})=>{let a=v(e);return y(`/projects/${a}`,{method:"GET"})},Q=async({appName:e,filePath:a,branch:i})=>{let s=v(e);return y(`/projects/${s}/repository/files/${encodeURIComponent(a)}?ref=${i}`,{method:"GET"})},A=async({appName:e,filePath:a,branch:i,content:s,commit_message:r})=>{let c=v(e);return y(`/projects/${c}/repository/files/${encodeURIComponent(a)}`,{method:"PUT",body:JSON.stringify({branch:i,content:s,commit_message:r})})};var X=[{id:1414,description:"\u5FAE\u4FE1\u5C0F\u7A0B\u5E8F\u4E4B\u5FAE\u5546\u57CE\uFF0C\u8FD9\u4E2A\u4EE3\u7801\u4E0D\u8981\u6CC4\u9732\u5230\u5916\u90E8\uFF0C\u53EF\u80FD\u6709\u5B89\u5168\u98CE\u9669",name:"wsc",name_with_namespace:"weapp / wsc",path:"wsc",path_with_namespace:"weapp/wsc",created_at:"2016-11-16T01:50:03.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:weapp/wsc.git",http_url_to_repo:"https://gitlab.qima-inc.com/weapp/wsc.git",web_url:"https://gitlab.qima-inc.com/weapp/wsc",readme_url:"https://gitlab.qima-inc.com/weapp/wsc/-/blob/master/README.md",avatar_url:"https://gitlab.qima-inc.com/uploads/-/system/project/avatar/1414/WechatIMG3.jpeg",forks_count:2,star_count:45,last_activity_at:"2025-07-12T12:28:42.617Z",namespace:{id:356,name:"weapp",path:"weapp",kind:"group",full_path:"weapp",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",web_url:"https://gitlab.qima-inc.com/groups/weapp"}},{id:11795,description:"",name:"wsc-tee-h5",name_with_namespace:"weapp / wsc-tee-h5",path:"wsc-tee-h5",path_with_namespace:"weapp/wsc-tee-h5",created_at:"2021-03-30T12:15:17.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:weapp/wsc-tee-h5.git",http_url_to_repo:"https://gitlab.qima-inc.com/weapp/wsc-tee-h5.git",web_url:"https://gitlab.qima-inc.com/weapp/wsc-tee-h5",readme_url:"https://gitlab.qima-inc.com/weapp/wsc-tee-h5/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:9,last_activity_at:"2025-07-12T12:28:38.823Z",namespace:{id:356,name:"weapp",path:"weapp",kind:"group",full_path:"weapp",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",web_url:"https://gitlab.qima-inc.com/groups/weapp"}},{id:12047,description:"",name:"ext-tee-wsc-trade",name_with_namespace:"weapp / ext-tee-wsc-trade",path:"ext-tee-wsc-trade",path_with_namespace:"weapp/ext-tee-wsc-trade",created_at:"2021-05-10T03:22:47.147Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:weapp/ext-tee-wsc-trade.git",http_url_to_repo:"https://gitlab.qima-inc.com/weapp/ext-tee-wsc-trade.git",web_url:"https://gitlab.qima-inc.com/weapp/ext-tee-wsc-trade",readme_url:"https://gitlab.qima-inc.com/weapp/ext-tee-wsc-trade/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:5,last_activity_at:"2025-07-12T12:28:38.016Z",namespace:{id:356,name:"weapp",path:"weapp",kind:"group",full_path:"weapp",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",web_url:"https://gitlab.qima-inc.com/groups/weapp"}},{id:11654,description:"\u8D44\u4EA7 - \u591A\u7AEF\u4E2D\u53F0\u5316\u6269\u5C55\u4ED3\u5E93",name:"ext-tee-assets",name_with_namespace:"weapp / ext-tee-assets",path:"ext-tee-assets",path_with_namespace:"weapp/ext-tee-assets",created_at:"2021-03-12T02:57:31.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:weapp/ext-tee-assets.git",http_url_to_repo:"https://gitlab.qima-inc.com/weapp/ext-tee-assets.git",web_url:"https://gitlab.qima-inc.com/weapp/ext-tee-assets",readme_url:"https://gitlab.qima-inc.com/weapp/ext-tee-assets/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-12T12:28:37.314Z",namespace:{id:356,name:"weapp",path:"weapp",kind:"group",full_path:"weapp",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",web_url:"https://gitlab.qima-inc.com/groups/weapp"}},{id:11809,description:`\u5546\u54C1 - \u591A\u7AEF\u4E2D\u53F0\u5316\u6269\u5C55\u4ED3\u5E93\r
`,name:"ext-tee-wsc-goods",name_with_namespace:"weapp / ext-tee-wsc-goods",path:"ext-tee-wsc-goods",path_with_namespace:"weapp/ext-tee-wsc-goods",created_at:"2021-04-01T06:24:03.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:weapp/ext-tee-wsc-goods.git",http_url_to_repo:"https://gitlab.qima-inc.com/weapp/ext-tee-wsc-goods.git",web_url:"https://gitlab.qima-inc.com/weapp/ext-tee-wsc-goods",readme_url:"https://gitlab.qima-inc.com/weapp/ext-tee-wsc-goods/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:3,last_activity_at:"2025-07-12T12:28:36.608Z",namespace:{id:356,name:"weapp",path:"weapp",kind:"group",full_path:"weapp",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",web_url:"https://gitlab.qima-inc.com/groups/weapp"}},{id:15409,description:"",name:"npmpublish",name_with_namespace:"xujiazheng / npmpublish",path:"npmpublish",path_with_namespace:"xujiazheng/npmpublish",created_at:"2025-06-17T07:09:53.775Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:xujiazheng/npmpublish.git",http_url_to_repo:"https://gitlab.qima-inc.com/xujiazheng/npmpublish.git",web_url:"https://gitlab.qima-inc.com/xujiazheng/npmpublish",readme_url:"https://gitlab.qima-inc.com/xujiazheng/npmpublish/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-12T06:09:29.454Z",namespace:{id:3020,name:"xujiazheng",path:"xujiazheng",kind:"user",full_path:"xujiazheng",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/xujiazheng"}},{id:10345,description:"\u96F6\u552E PC \u540E\u53F0\u5C65\u7EA6\u6A21\u5757",name:"retail-node-fulfillment",name_with_namespace:"retail-web / retail-node-fulfillment",path:"retail-node-fulfillment",path_with_namespace:"retail-web/retail-node-fulfillment",created_at:"2020-08-20T03:40:06.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:retail-web/retail-node-fulfillment.git",http_url_to_repo:"https://gitlab.qima-inc.com/retail-web/retail-node-fulfillment.git",web_url:"https://gitlab.qima-inc.com/retail-web/retail-node-fulfillment",readme_url:"https://gitlab.qima-inc.com/retail-web/retail-node-fulfillment/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:1,last_activity_at:"2025-07-12T05:47:42.893Z",namespace:{id:579,name:"retail-web",path:"retail-web",kind:"group",full_path:"retail-web",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/579/box-packing-peanut-512-min.png",web_url:"https://gitlab.qima-inc.com/groups/retail-web"}},{id:5808,description:"",name:"retail-node-order",name_with_namespace:"retail-web / retail-node-order",path:"retail-node-order",path_with_namespace:"retail-web/retail-node-order",created_at:"2018-10-10T02:20:24.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:retail-web/retail-node-order.git",http_url_to_repo:"https://gitlab.qima-inc.com/retail-web/retail-node-order.git",web_url:"https://gitlab.qima-inc.com/retail-web/retail-node-order",readme_url:"https://gitlab.qima-inc.com/retail-web/retail-node-order/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:4,last_activity_at:"2025-07-12T05:47:28.590Z",namespace:{id:579,name:"retail-web",path:"retail-web",kind:"group",full_path:"retail-web",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/579/box-packing-peanut-512-min.png",web_url:"https://gitlab.qima-inc.com/groups/retail-web"}},{id:4950,description:"Iron PC \u62C6\u5206\u4E1A\u52A1\uFF1A\u8BA2\u5355",name:"wsc-pc-trade",name_with_namespace:"wsc-node / wsc-pc-trade",path:"wsc-pc-trade",path_with_namespace:"wsc-node/wsc-pc-trade",created_at:"2018-06-11T03:19:44.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/wsc-pc-trade.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-trade.git",web_url:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-trade",readme_url:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-trade/-/blob/master/README.md",avatar_url:null,forks_count:1,star_count:6,last_activity_at:"2025-07-12T05:46:17.136Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:11108,description:"\u50A8\u503Cpc",name:"retail-pc-prepaid",name_with_namespace:"retail-web / retail-pc-prepaid",path:"retail-pc-prepaid",path_with_namespace:"retail-web/retail-pc-prepaid",created_at:"2020-12-16T03:42:43.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:retail-web/retail-pc-prepaid.git",http_url_to_repo:"https://gitlab.qima-inc.com/retail-web/retail-pc-prepaid.git",web_url:"https://gitlab.qima-inc.com/retail-web/retail-pc-prepaid",readme_url:"https://gitlab.qima-inc.com/retail-web/retail-pc-prepaid/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:2,last_activity_at:"2025-07-12T03:15:22.684Z",namespace:{id:579,name:"retail-web",path:"retail-web",kind:"group",full_path:"retail-web",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/579/box-packing-peanut-512-min.png",web_url:"https://gitlab.qima-inc.com/groups/retail-web"}},{id:11109,description:"\u50A8\u503CH5",name:"retail-h5-prepaid",name_with_namespace:"retail-web / retail-h5-prepaid",path:"retail-h5-prepaid",path_with_namespace:"retail-web/retail-h5-prepaid",created_at:"2020-12-16T03:45:15.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:retail-web/retail-h5-prepaid.git",http_url_to_repo:"https://gitlab.qima-inc.com/retail-web/retail-h5-prepaid.git",web_url:"https://gitlab.qima-inc.com/retail-web/retail-h5-prepaid",readme_url:"https://gitlab.qima-inc.com/retail-web/retail-h5-prepaid/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:2,last_activity_at:"2025-07-12T00:05:12.340Z",namespace:{id:579,name:"retail-web",path:"retail-web",kind:"group",full_path:"retail-web",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/579/box-packing-peanut-512-min.png",web_url:"https://gitlab.qima-inc.com/groups/retail-web"}},{id:10002,description:"\u591A\u7AEF\u6846\u67B6",name:"tee",name_with_namespace:"fe-middle-platform / tee",path:"tee",path_with_namespace:"fe-middle-platform/tee",created_at:"2020-07-08T02:32:42.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe-middle-platform/tee.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe-middle-platform/tee.git",web_url:"https://gitlab.qima-inc.com/fe-middle-platform/tee",readme_url:"https://gitlab.qima-inc.com/fe-middle-platform/tee/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:3,last_activity_at:"2025-07-12T00:05:11.306Z",namespace:{id:2081,name:"fe-middle-platform",path:"fe-middle-platform",kind:"group",full_path:"fe-middle-platform",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/2081/images.png",web_url:"https://gitlab.qima-inc.com/groups/fe-middle-platform"}},{id:10075,description:"\u591A\u7AEF\u5C0F\u7A0B\u5E8F base \u4ED3\u5E93",name:"wsc-tee-base",name_with_namespace:"weapp / wsc-tee-base",path:"wsc-tee-base",path_with_namespace:"weapp/wsc-tee-base",created_at:"2020-07-16T02:33:09.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:weapp/wsc-tee-base.git",http_url_to_repo:"https://gitlab.qima-inc.com/weapp/wsc-tee-base.git",web_url:"https://gitlab.qima-inc.com/weapp/wsc-tee-base",readme_url:"https://gitlab.qima-inc.com/weapp/wsc-tee-base/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:1,last_activity_at:"2025-07-12T00:05:10.718Z",namespace:{id:356,name:"weapp",path:"weapp",kind:"group",full_path:"weapp",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",web_url:"https://gitlab.qima-inc.com/groups/weapp"}},{id:6197,description:"\u96F6\u552E\u524D\u7AEF\uFF08\u5546\u54C1\uFF09 Node.js \u9879\u76EE",name:"retail-node-goods",name_with_namespace:"retail-web / retail-node-goods",path:"retail-node-goods",path_with_namespace:"retail-web/retail-node-goods",created_at:"2018-12-05T02:20:41.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:retail-web/retail-node-goods.git",http_url_to_repo:"https://gitlab.qima-inc.com/retail-web/retail-node-goods.git",web_url:"https://gitlab.qima-inc.com/retail-web/retail-node-goods",readme_url:"https://gitlab.qima-inc.com/retail-web/retail-node-goods/-/blob/master/README.md",avatar_url:"https://gitlab.qima-inc.com/uploads/-/system/project/avatar/6197/ls-logo_4.jpg",forks_count:0,star_count:5,last_activity_at:"2025-07-12T00:05:07.920Z",namespace:{id:579,name:"retail-web",path:"retail-web",kind:"group",full_path:"retail-web",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/579/box-packing-peanut-512-min.png",web_url:"https://gitlab.qima-inc.com/groups/retail-web"}},{id:6081,description:"\u96F6\u552E\u524D\u7AEF\u5E97\u94FA Node \u9879\u76EE",name:"retail-node-shop",name_with_namespace:"retail-web / retail-node-shop",path:"retail-node-shop",path_with_namespace:"retail-web/retail-node-shop",created_at:"2018-11-21T12:00:11.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:retail-web/retail-node-shop.git",http_url_to_repo:"https://gitlab.qima-inc.com/retail-web/retail-node-shop.git",web_url:"https://gitlab.qima-inc.com/retail-web/retail-node-shop",readme_url:"https://gitlab.qima-inc.com/retail-web/retail-node-shop/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:3,last_activity_at:"2025-07-12T00:05:06.181Z",namespace:{id:579,name:"retail-web",path:"retail-web",kind:"group",full_path:"retail-web",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/579/box-packing-peanut-512-min.png",web_url:"https://gitlab.qima-inc.com/groups/retail-web"}},{id:6184,description:"\u4E00\u4E9B\u5C0F\u7684\u4E1A\u52A1\u6A21\u5757\uFF0C\u76EE\u524D\u5305\u542B dashboard tool minapp",name:"retail-node-v2",name_with_namespace:"retail-web / retail-node-v2",path:"retail-node-v2",path_with_namespace:"retail-web/retail-node-v2",created_at:"2018-12-04T02:59:02.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:retail-web/retail-node-v2.git",http_url_to_repo:"https://gitlab.qima-inc.com/retail-web/retail-node-v2.git",web_url:"https://gitlab.qima-inc.com/retail-web/retail-node-v2",readme_url:"https://gitlab.qima-inc.com/retail-web/retail-node-v2/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-12T00:05:05.715Z",namespace:{id:579,name:"retail-web",path:"retail-web",kind:"group",full_path:"retail-web",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/579/box-packing-peanut-512-min.png",web_url:"https://gitlab.qima-inc.com/groups/retail-web"}},{id:5136,description:"Iron H5 \u4E1A\u52A1\u62C6\u5206\uFF1A\u8D44\u4EA7",name:"wsc-h5-assets",name_with_namespace:"wsc-node / wsc-h5-assets",path:"wsc-h5-assets",path_with_namespace:"wsc-node/wsc-h5-assets",created_at:"2018-07-11T03:43:04.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/wsc-h5-assets.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-assets.git",web_url:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-assets",readme_url:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-assets/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:1,last_activity_at:"2025-07-11T22:00:01.712Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:6575,description:"\u7EDF\u4E00\u6536\u94F6\u53F0\u9879\u76EE",name:"assets-cashier-source",name_with_namespace:"fe-assets / assets-cashier-source",path:"assets-cashier-source",path_with_namespace:"fe-assets/assets-cashier-source",created_at:"2019-01-16T09:33:54.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe-assets/assets-cashier-source.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe-assets/assets-cashier-source.git",web_url:"https://gitlab.qima-inc.com/fe-assets/assets-cashier-source",readme_url:"https://gitlab.qima-inc.com/fe-assets/assets-cashier-source/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-11T22:00:01.394Z",namespace:{id:1442,name:"fe-assets",path:"fe-assets",kind:"group",full_path:"fe-assets",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/fe-assets"}},{id:4953,description:"",name:"cert",name_with_namespace:"fe / cert",path:"cert",path_with_namespace:"fe/cert",created_at:"2018-06-11T06:19:23.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/cert.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/cert.git",web_url:"https://gitlab.qima-inc.com/fe/cert",readme_url:"https://gitlab.qima-inc.com/fe/cert/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-11T22:00:01.219Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:5243,description:"\u5BF9\u8D26\u5E73\u53F0\uFF0C\u524D\u7AEF\u5E94\u7528",name:"pay-check-web",name_with_namespace:"fe / pay-check-web",path:"pay-check-web",path_with_namespace:"fe/pay-check-web",created_at:"2018-07-25T02:33:38.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/pay-check-web.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/pay-check-web.git",web_url:"https://gitlab.qima-inc.com/fe/pay-check-web",readme_url:"https://gitlab.qima-inc.com/fe/pay-check-web/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-11T22:00:00.901Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:4949,description:"Iron PC \u62C6\u5206\u4E1A\u52A1\uFF1A\u5546\u54C1",name:"wsc-pc-goods",name_with_namespace:"wsc-node / wsc-pc-goods",path:"wsc-pc-goods",path_with_namespace:"wsc-node/wsc-pc-goods",created_at:"2018-06-11T03:15:40.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/wsc-pc-goods.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-goods.git",web_url:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-goods",readme_url:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-goods/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:7,last_activity_at:"2025-07-11T16:49:43.992Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:15347,description:"\u6709\u8D5E\u78B0\u78B0\u8D34\u5C0F\u7A0B\u5E8F\uFF08\u652F\u4ED8\u5B9D\uFF0C\u5FAE\u4FE1\uFF09\u9879\u76EE",name:"zan-ppt",name_with_namespace:"weapp / zan-ppt",path:"zan-ppt",path_with_namespace:"weapp/zan-ppt",created_at:"2025-05-30T08:54:27.060Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:weapp/zan-ppt.git",http_url_to_repo:"https://gitlab.qima-inc.com/weapp/zan-ppt.git",web_url:"https://gitlab.qima-inc.com/weapp/zan-ppt",readme_url:"https://gitlab.qima-inc.com/weapp/zan-ppt/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-11T10:42:32.968Z",namespace:{id:356,name:"weapp",path:"weapp",kind:"group",full_path:"weapp",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",web_url:"https://gitlab.qima-inc.com/groups/weapp"}},{id:11993,description:"\u63D0\u4F9B SCRM \u516C\u7528\u80FD\u529B\u7684 NPM \u5305",name:"Scrm Packages Mono",name_with_namespace:"fe / Scrm Packages Mono",path:"scrm-packages-mono",path_with_namespace:"fe/scrm-packages-mono",created_at:"2021-04-27T02:04:28.375Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/scrm-packages-mono.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/scrm-packages-mono.git",web_url:"https://gitlab.qima-inc.com/fe/scrm-packages-mono",readme_url:"https://gitlab.qima-inc.com/fe/scrm-packages-mono/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-11T10:28:47.173Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:10798,description:`\u6709\u8D5E SCRM B \u7AEF\u53D1\u5E03\u4ED3\u5E93\u3002\r
\u6CE8\u610F\uFF1A\u4E0D\u662F\u6E90\u7801\u4ED3\u5E93\uFF0C\u4EC5\u7528\u4E8E\u53D1\u5E03\u3002`,name:"scrm-b-pc-dist",name_with_namespace:"fe / scrm-b-pc-dist",path:"scrm-b-pc-dist",path_with_namespace:"fe/scrm-b-pc-dist",created_at:"2020-10-29T07:31:27.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/scrm-b-pc-dist.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/scrm-b-pc-dist.git",web_url:"https://gitlab.qima-inc.com/fe/scrm-b-pc-dist",readme_url:null,avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-11T10:24:33.509Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:4947,description:"Iron PC \u62C6\u5206\u4E1A\u52A1\uFF1A\u6982\u51B5\u3001\u5E97\u94FA",name:"wsc-pc-shop",name_with_namespace:"wsc-node / wsc-pc-shop",path:"wsc-pc-shop",path_with_namespace:"wsc-node/wsc-pc-shop",created_at:"2018-06-11T03:07:57.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/wsc-pc-shop.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-shop.git",web_url:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-shop",readme_url:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-shop/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:7,last_activity_at:"2025-07-11T10:06:12.352Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:14787,description:"",name:"Ci Cache",name_with_namespace:"fe / Ci Cache",path:"ci_cache",path_with_namespace:"fe/ci_cache",created_at:"2023-08-29T08:54:43.183Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/ci_cache.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/ci_cache.git",web_url:"https://gitlab.qima-inc.com/fe/ci_cache",readme_url:"https://gitlab.qima-inc.com/fe/ci_cache/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-11T09:53:33.282Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:10775,description:"\u6709\u8D5E SCRM monorepo",name:"scrm-mono",name_with_namespace:"fe / scrm-mono",path:"scrm-mono",path_with_namespace:"fe/scrm-mono",created_at:"2020-10-27T09:43:31.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/scrm-mono.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/scrm-mono.git",web_url:"https://gitlab.qima-inc.com/fe/scrm-mono",readme_url:"https://gitlab.qima-inc.com/fe/scrm-mono/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:5,last_activity_at:"2025-07-11T09:51:01.617Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:4585,description:"The multi package repo for IM project",name:"im-web",name_with_namespace:"fe / im-web",path:"im-web",path_with_namespace:"fe/im-web",created_at:"2018-04-18T06:47:30.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/im-web.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/im-web.git",web_url:"https://gitlab.qima-inc.com/fe/im-web",readme_url:"https://gitlab.qima-inc.com/fe/im-web/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:3,last_activity_at:"2025-07-11T09:48:58.847Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:15150,description:"\u79C1\u57DF\u76F4\u64ADext",name:"ext-tee-live",name_with_namespace:"private-live / ext-tee-live",path:"ext-tee-live",path_with_namespace:"private-live/ext-tee-live",created_at:"2024-12-26T03:09:02.372Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:private-live/ext-tee-live.git",http_url_to_repo:"https://gitlab.qima-inc.com/private-live/ext-tee-live.git",web_url:"https://gitlab.qima-inc.com/private-live/ext-tee-live",readme_url:"https://gitlab.qima-inc.com/private-live/ext-tee-live/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:1,last_activity_at:"2025-07-11T09:10:01.776Z",namespace:{id:3840,name:"private-live",path:"private-live",kind:"group",full_path:"private-live",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/private-live"}},{id:10567,description:"",name:"decorate-tee",name_with_namespace:"weapp / decorate-tee",path:"decorate-tee",path_with_namespace:"weapp/decorate-tee",created_at:"2020-09-15T12:44:03.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:weapp/decorate-tee.git",http_url_to_repo:"https://gitlab.qima-inc.com/weapp/decorate-tee.git",web_url:"https://gitlab.qima-inc.com/weapp/decorate-tee",readme_url:"https://gitlab.qima-inc.com/weapp/decorate-tee/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:2,last_activity_at:"2025-07-11T08:53:13.443Z",namespace:{id:356,name:"weapp",path:"weapp",kind:"group",full_path:"weapp",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",web_url:"https://gitlab.qima-inc.com/groups/weapp"}},{id:14921,description:"",name:"fe-test-service",name_with_namespace:"fe-middle-platform / fe-test-service",path:"fe-test-service",path_with_namespace:"fe-middle-platform/fe-test-service",created_at:"2024-03-05T08:02:20.473Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe-middle-platform/fe-test-service.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe-middle-platform/fe-test-service.git",web_url:"https://gitlab.qima-inc.com/fe-middle-platform/fe-test-service",readme_url:"https://gitlab.qima-inc.com/fe-middle-platform/fe-test-service/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-11T08:29:13.788Z",namespace:{id:2081,name:"fe-middle-platform",path:"fe-middle-platform",kind:"group",full_path:"fe-middle-platform",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/2081/images.png",web_url:"https://gitlab.qima-inc.com/groups/fe-middle-platform"}},{id:11628,description:"\u5FAE\u5546\u57CE\u88C5\u4FEE  - \u591A\u7AEF\u4E2D\u53F0\u5316\u6269\u5C55\u4ED3\u5E93",name:"ext-tee-wsc-decorate",name_with_namespace:"weapp / ext-tee-wsc-decorate",path:"ext-tee-wsc-decorate",path_with_namespace:"weapp/ext-tee-wsc-decorate",created_at:"2021-03-09T02:50:23.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:weapp/ext-tee-wsc-decorate.git",http_url_to_repo:"https://gitlab.qima-inc.com/weapp/ext-tee-wsc-decorate.git",web_url:"https://gitlab.qima-inc.com/weapp/ext-tee-wsc-decorate",readme_url:"https://gitlab.qima-inc.com/weapp/ext-tee-wsc-decorate/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:1,last_activity_at:"2025-07-11T08:26:21.465Z",namespace:{id:356,name:"weapp",path:"weapp",kind:"group",full_path:"weapp",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",web_url:"https://gitlab.qima-inc.com/groups/weapp"}},{id:12207,description:"\u8D27\u67B6\u5C0F\u7A0B\u5E8F\u8DE8\u7AEF\u4ED3\u5E93",name:"ext-tee-retail-shelf",name_with_namespace:"retail-web / retail-tee / ext-tee-retail-shelf",path:"ext-tee-retail-shelf",path_with_namespace:"retail-web/retail-tee/ext-tee-retail-shelf",created_at:"2021-06-04T08:29:50.598Z",default_branch:"dev",tag_list:[],ssh_url_to_repo:"***********************:retail-web/retail-tee/ext-tee-retail-shelf.git",http_url_to_repo:"https://gitlab.qima-inc.com/retail-web/retail-tee/ext-tee-retail-shelf.git",web_url:"https://gitlab.qima-inc.com/retail-web/retail-tee/ext-tee-retail-shelf",readme_url:"https://gitlab.qima-inc.com/retail-web/retail-tee/ext-tee-retail-shelf/-/blob/dev/README.md",avatar_url:null,forks_count:0,star_count:2,last_activity_at:"2025-07-11T08:24:33.817Z",namespace:{id:2872,name:"retail-tee",path:"retail-tee",kind:"group",full_path:"retail-web/retail-tee",parent_id:579,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/retail-web/retail-tee"}},{id:10755,description:"\u4F01\u4E1A\u5FAE\u4FE1\u52A9\u624BB\u7AEF monorepo",name:"weass-b-mono",name_with_namespace:"fe / weass-b-mono",path:"weass-b-mono",path_with_namespace:"fe/weass-b-mono",created_at:"2020-10-23T11:01:07.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/weass-b-mono.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/weass-b-mono.git",web_url:"https://gitlab.qima-inc.com/fe/weass-b-mono",readme_url:"https://gitlab.qima-inc.com/fe/weass-b-mono/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:4,last_activity_at:"2025-07-11T08:22:27.003Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:5182,description:"\u5FAE\u5546\u57CE\u6D88\u606F\u76F8\u5173\u4E1A\u52A1\u6E90\u7801\u4ED3\u5E93",name:"wsc-pc-message-web",name_with_namespace:"fe / wsc-pc-message-web",path:"wsc-pc-message-web",path_with_namespace:"fe/wsc-pc-message-web",created_at:"2018-07-16T12:27:17.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/wsc-pc-message-web.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/wsc-pc-message-web.git",web_url:"https://gitlab.qima-inc.com/fe/wsc-pc-message-web",readme_url:"https://gitlab.qima-inc.com/fe/wsc-pc-message-web/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:2,last_activity_at:"2025-07-11T08:21:11.263Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:14566,description:"",name:"jarvis-front",name_with_namespace:"fe / jarvis-front",path:"jarvis-front",path_with_namespace:"fe/jarvis-front",created_at:"2023-03-20T10:04:36.182Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/jarvis-front.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/jarvis-front.git",web_url:"https://gitlab.qima-inc.com/fe/jarvis-front",readme_url:"https://gitlab.qima-inc.com/fe/jarvis-front/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-11T08:15:40.876Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:12951,description:`\u6709\u8D5E\u4E91\u6587\u6863\u4E2D\u5FC3\u7684\u6587\u6863\u6E90\u6587\u4EF6\r
\u8BE5\u4ED3\u5E93\u975E\u6709\u8D5E\u4E91\u6587\u6863\u4E2D\u5FC3\u7684\u6E90\u4EE3\u7801`,name:"CloudDocs",name_with_namespace:"youzan / CloudDocs",path:"cloudDocs",path_with_namespace:"youzan/cloudDocs",created_at:"2021-10-09T07:35:56.865Z",default_branch:"preview",tag_list:[],ssh_url_to_repo:"***********************:youzan/cloudDocs.git",http_url_to_repo:"https://gitlab.qima-inc.com/youzan/cloudDocs.git",web_url:"https://gitlab.qima-inc.com/youzan/cloudDocs",readme_url:"https://gitlab.qima-inc.com/youzan/cloudDocs/-/blob/preview/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-11T08:11:11.697Z",namespace:{id:5,name:"youzan",path:"youzan",kind:"group",full_path:"youzan",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/5/cda5c81e0c64c791c4c190c1efcc1eaa.png",web_url:"https://gitlab.qima-inc.com/groups/youzan"}},{id:9988,description:"\u88C5\u4FEE\u76F8\u5173\u4E1A\u52A1",name:"wsc-h5-decorate",name_with_namespace:"wsc-node / wsc-h5-decorate",path:"wsc-h5-decorate",path_with_namespace:"wsc-node/wsc-h5-decorate",created_at:"2020-07-06T09:26:12.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/wsc-h5-decorate.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-decorate.git",web_url:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-decorate",readme_url:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-decorate/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:4,last_activity_at:"2025-07-11T08:01:19.422Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:15198,description:"",name:"ai-test",name_with_namespace:"fe / ai-test",path:"ai-test",path_with_namespace:"fe/ai-test",created_at:"2025-03-03T03:20:44.577Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/ai-test.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/ai-test.git",web_url:"https://gitlab.qima-inc.com/fe/ai-test",readme_url:"https://gitlab.qima-inc.com/fe/ai-test/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-11T08:00:26.005Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:6796,description:"",name:"wsc-h5-showcase-components",name_with_namespace:"fe / wsc-h5-showcase-components",path:"wsc-h5-showcase-components",path_with_namespace:"fe/wsc-h5-showcase-components",created_at:"2019-02-28T09:57:05.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/wsc-h5-showcase-components.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/wsc-h5-showcase-components.git",web_url:"https://gitlab.qima-inc.com/fe/wsc-h5-showcase-components",readme_url:"https://gitlab.qima-inc.com/fe/wsc-h5-showcase-components/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:3,last_activity_at:"2025-07-11T07:55:21.029Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:15200,description:"",name:"ai-test-chrome-extension",name_with_namespace:"fe / ai-test-chrome-extension",path:"ai-test-chrome-extension",path_with_namespace:"fe/ai-test-chrome-extension",created_at:"2025-03-03T10:55:02.824Z",default_branch:"main",tag_list:[],ssh_url_to_repo:"***********************:fe/ai-test-chrome-extension.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/ai-test-chrome-extension.git",web_url:"https://gitlab.qima-inc.com/fe/ai-test-chrome-extension",readme_url:"https://gitlab.qima-inc.com/fe/ai-test-chrome-extension/-/blob/main/README.md",avatar_url:"https://gitlab.qima-inc.com/uploads/-/system/project/avatar/15200/20250319-173543.png",forks_count:0,star_count:0,last_activity_at:"2025-07-11T07:54:16.638Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:14597,description:"",name:"*********************",name_with_namespace:"fe / *********************",path:"*********************",path_with_namespace:"fe/*********************",created_at:"2023-04-19T09:16:07.046Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/*********************.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/*********************.git",web_url:"https://gitlab.qima-inc.com/fe/*********************",readme_url:null,avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-11T07:41:21.045Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:14589,description:"",name:"jarvis-commander-mono",name_with_namespace:"fe / jarvis-commander-mono",path:"jarvis-commander-mono",path_with_namespace:"fe/jarvis-commander-mono",created_at:"2023-04-13T02:19:27.964Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/jarvis-commander-mono.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/jarvis-commander-mono.git",web_url:"https://gitlab.qima-inc.com/fe/jarvis-commander-mono",readme_url:"https://gitlab.qima-inc.com/fe/jarvis-commander-mono/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:1,last_activity_at:"2025-07-11T07:33:31.326Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:4576,description:"Iron H5 \u62C6\u5206\u4E1A\u52A1\uFF1A\u5E94\u7528\u8425\u9500",name:"wsc-h5-ump",name_with_namespace:"wsc-node / wsc-h5-ump",path:"wsc-h5-ump",path_with_namespace:"wsc-node/wsc-h5-ump",created_at:"2018-04-17T07:10:05.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/wsc-h5-ump.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-ump.git",web_url:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-ump",readme_url:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-ump/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:3,last_activity_at:"2025-07-11T07:30:44.295Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:14448,description:"\u8425\u9500\u753B\u5E03\u524D\u7AEF\u4ED3\u5E93",name:"ma-front",name_with_namespace:"fe / ma-front",path:"ma-front",path_with_namespace:"fe/ma-front",created_at:"2022-11-30T06:02:24.470Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/ma-front.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/ma-front.git",web_url:"https://gitlab.qima-inc.com/fe/ma-front",readme_url:"https://gitlab.qima-inc.com/fe/ma-front/-/blob/master/README.md",avatar_url:"https://gitlab.qima-inc.com/uploads/-/system/project/avatar/14448/00000105.jpg",forks_count:0,star_count:1,last_activity_at:"2025-07-11T07:26:44.775Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:11520,description:"",name:"guide-b-h5",name_with_namespace:"wsc-node / guide-b-h5",path:"guide-b-h5",path_with_namespace:"wsc-node/guide-b-h5",created_at:"2021-02-19T03:58:17.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/guide-b-h5.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/guide-b-h5.git",web_url:"https://gitlab.qima-inc.com/wsc-node/guide-b-h5",readme_url:"https://gitlab.qima-inc.com/wsc-node/guide-b-h5/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:3,last_activity_at:"2025-07-11T07:18:43.591Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:4555,description:"Iron H5 \u62C6\u5206\u4E1A\u52A1\uFF1A\u5FAE\u9875\u9762\u3001\u5546\u54C1\u3001\u5E97\u94FA",name:"wsc-h5-shop",name_with_namespace:"wsc-node / wsc-h5-shop",path:"wsc-h5-shop",path_with_namespace:"wsc-node/wsc-h5-shop",created_at:"2018-04-13T02:22:55.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/wsc-h5-shop.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-shop.git",web_url:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-shop",readme_url:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-shop/-/blob/master/README.md",avatar_url:null,forks_count:1,star_count:8,last_activity_at:"2025-07-11T07:16:16.403Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:4573,description:"Iron \u62C6\u5206\u4E1A\u52A1\uFF1A\u4EA4\u6613\u3001\u4E0B\u5355\u3001\u8BA2\u5355",name:"wsc-h5-trade",name_with_namespace:"wsc-node / wsc-h5-trade",path:"wsc-h5-trade",path_with_namespace:"wsc-node/wsc-h5-trade",created_at:"2018-04-17T03:20:29.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/wsc-h5-trade.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-trade.git",web_url:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-trade",readme_url:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-trade/-/blob/master/README.md",avatar_url:"https://gitlab.qima-inc.com/uploads/-/system/project/avatar/4573/Node.js__2_.png",forks_count:0,star_count:13,last_activity_at:"2025-07-11T07:01:47.350Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:8911,description:"\u5206\u9500\u5458\uFF0C\u4E91\u5206\u9500 \u6D88\u8D39\u8005\u7AEF\u4E1A\u52A1",name:"wsc-h5-salesman",name_with_namespace:"wsc-node / wsc-h5-salesman",path:"wsc-h5-salesman",path_with_namespace:"wsc-node/wsc-h5-salesman",created_at:"2020-01-17T03:51:32.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/wsc-h5-salesman.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-salesman.git",web_url:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-salesman",readme_url:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-salesman/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:4,last_activity_at:"2025-07-11T06:53:45.475Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:5046,description:"Iron PC \u62C6\u5206\u4E1A\u52A1\uFF1ASCRM",name:"wsc-pc-scrm",name_with_namespace:"wsc-node / wsc-pc-scrm",path:"wsc-pc-scrm",path_with_namespace:"wsc-node/wsc-pc-scrm",created_at:"2018-06-27T07:40:55.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/wsc-pc-scrm.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-scrm.git",web_url:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-scrm",readme_url:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-scrm/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:5,last_activity_at:"2025-07-11T06:44:15.482Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:1979,description:"\u5FAE\u5546\u57CEReact\u4E1A\u52A1\u7EC4\u4EF6",name:"react-components",name_with_namespace:"fe / react-components",path:"react-components",path_with_namespace:"fe/react-components",created_at:"2017-02-21T09:36:48.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/react-components.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/react-components.git",web_url:"https://gitlab.qima-inc.com/fe/react-components",readme_url:"https://gitlab.qima-inc.com/fe/react-components/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:23,last_activity_at:"2025-07-11T06:27:31.161Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:11992,description:"\u5BFC\u8D2DB\u7AEFpc",name:"guide-b-pc",name_with_namespace:"fe / guide-b-pc",path:"guide-b-pc",path_with_namespace:"fe/guide-b-pc",created_at:"2021-04-26T13:29:38.263Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/guide-b-pc.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/guide-b-pc.git",web_url:"https://gitlab.qima-inc.com/fe/guide-b-pc",readme_url:"https://gitlab.qima-inc.com/fe/guide-b-pc/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:1,last_activity_at:"2025-07-11T06:23:50.824Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:15222,description:"\u4ECE github midscene https://github.com/web-infra-dev/midscene  \u5206\u53C9\u8FC7\u6765\u7684",name:"midscene",name_with_namespace:"fe / midscene",path:"midscene",path_with_namespace:"fe/midscene",created_at:"2025-03-06T08:06:25.682Z",default_branch:"main",tag_list:[],ssh_url_to_repo:"***********************:fe/midscene.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/midscene.git",web_url:"https://gitlab.qima-inc.com/fe/midscene",readme_url:"https://gitlab.qima-inc.com/fe/midscene/-/blob/main/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-11T06:19:12.365Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:3780,description:"\u5FAE\u9875\u9762\u6742\u5FD7\u8001\u6570\u636E-\u65B0\u6570\u636E-captain\u7EC4\u4EF6\u7684\u8F6C\u5316\u51FD\u6570",name:"feature-adaptor",name_with_namespace:"fe / feature-adaptor",path:"feature-adaptor",path_with_namespace:"fe/feature-adaptor",created_at:"2017-11-11T09:05:08.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/feature-adaptor.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/feature-adaptor.git",web_url:"https://gitlab.qima-inc.com/fe/feature-adaptor",readme_url:"https://gitlab.qima-inc.com/fe/feature-adaptor/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-11T06:16:48.013Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:9989,description:"",name:"wsc-pc-decorate",name_with_namespace:"wsc-node / wsc-pc-decorate",path:"wsc-pc-decorate",path_with_namespace:"wsc-node/wsc-pc-decorate",created_at:"2020-07-06T09:26:49.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/wsc-pc-decorate.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-decorate.git",web_url:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-decorate",readme_url:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-decorate/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:5,last_activity_at:"2025-07-11T06:12:15.096Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:15232,description:"",name:"ai-data",name_with_namespace:"fe / ai-data",path:"ai-data",path_with_namespace:"fe/ai-data",created_at:"2025-03-11T11:57:03.949Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/ai-data.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/ai-data.git",web_url:"https://gitlab.qima-inc.com/fe/ai-data",readme_url:"https://gitlab.qima-inc.com/fe/ai-data/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-11T06:09:16.489Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:14209,description:"ranta \u517C\u5BB9\u8001\u5B9A\u5236",name:"ranta-yun-adapter",name_with_namespace:"fe-middle-platform / ranta-yun-adapter",path:"ranta-yun-adapter",path_with_namespace:"fe-middle-platform/ranta-yun-adapter",created_at:"2022-05-27T03:33:41.661Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe-middle-platform/ranta-yun-adapter.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe-middle-platform/ranta-yun-adapter.git",web_url:"https://gitlab.qima-inc.com/fe-middle-platform/ranta-yun-adapter",readme_url:"https://gitlab.qima-inc.com/fe-middle-platform/ranta-yun-adapter/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-11T04:06:41.784Z",namespace:{id:2081,name:"fe-middle-platform",path:"fe-middle-platform",kind:"group",full_path:"fe-middle-platform",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/2081/images.png",web_url:"https://gitlab.qima-inc.com/groups/fe-middle-platform"}},{id:14891,description:"\u52A0\u6211\u667A\u80FD\u524D\u7AEF\u653E\u5728\u5FAE\u5546\u57CE\u5185\u7684\u9875\u9762",name:"wsc-pc-jiawo",name_with_namespace:"zanai / wsc-pc-jiawo",path:"wsc-pc-jiawo",path_with_namespace:"zanai/wsc-pc-jiawo",created_at:"2024-01-22T02:26:06.877Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:zanai/wsc-pc-jiawo.git",http_url_to_repo:"https://gitlab.qima-inc.com/zanai/wsc-pc-jiawo.git",web_url:"https://gitlab.qima-inc.com/zanai/wsc-pc-jiawo",readme_url:"https://gitlab.qima-inc.com/zanai/wsc-pc-jiawo/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-11T03:48:55.878Z",namespace:{id:3740,name:"zanai",path:"zanai",kind:"group",full_path:"zanai",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/zanai"}},{id:4952,description:"Iron PC \u62C6\u5206\u4E1A\u52A1\uFF1A\u6570\u636E",name:"wsc-pc-statcenter",name_with_namespace:"wsc-node / wsc-pc-statcenter",path:"wsc-pc-statcenter",path_with_namespace:"wsc-node/wsc-pc-statcenter",created_at:"2018-06-11T03:26:10.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/wsc-pc-statcenter.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-statcenter.git",web_url:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-statcenter",readme_url:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-statcenter/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-11T03:45:08.722Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:14974,description:"",name:"garden-echo",name_with_namespace:"fe / garden-echo",path:"garden-echo",path_with_namespace:"fe/garden-echo",created_at:"2024-05-22T09:52:32.756Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/garden-echo.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/garden-echo.git",web_url:"https://gitlab.qima-inc.com/fe/garden-echo",readme_url:"https://gitlab.qima-inc.com/fe/garden-echo/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-11T03:43:39.964Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:15401,description:"\u4E2D\u53F0\u5316 H5 \u5546\u5BB6\u7AEF",name:"admin-tee-h5-app",name_with_namespace:"fe / admin-tee-h5-app",path:"admin-tee-h5-app",path_with_namespace:"fe/admin-tee-h5-app",created_at:"2025-06-12T07:15:43.446Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/admin-tee-h5-app.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/admin-tee-h5-app.git",web_url:"https://gitlab.qima-inc.com/fe/admin-tee-h5-app",readme_url:"https://gitlab.qima-inc.com/fe/admin-tee-h5-app/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-11T02:50:27.063Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:10757,description:"\u4F01\u4E1A\u5FAE\u4FE1\u52A9\u624BB\u7AEFPC\u5E94\u7528\u90E8\u7F72\u4ED3\u5E93",name:"weass-b-pc-dist",name_with_namespace:"fe / weass-b-pc-dist",path:"weass-b-pc-dist",path_with_namespace:"fe/weass-b-pc-dist",created_at:"2020-10-23T11:04:37.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/weass-b-pc-dist.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/weass-b-pc-dist.git",web_url:"https://gitlab.qima-inc.com/fe/weass-b-pc-dist",readme_url:null,avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-11T02:47:08.375Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:8648,description:"\u5206\u9500\u5458\uFF0C\u4E91\u5206\u9500 \u5546\u5BB6\u8005\u7AEF\u4E1A\u52A1",name:"wsc-pc-salesman",name_with_namespace:"wsc-node / wsc-pc-salesman",path:"wsc-pc-salesman",path_with_namespace:"wsc-node/wsc-pc-salesman",created_at:"2019-11-28T01:21:32.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/wsc-pc-salesman.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-salesman.git",web_url:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-salesman",readme_url:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-salesman/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:5,last_activity_at:"2025-07-11T02:38:23.024Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:14318,description:"",name:"wsc-tee-goods-common",name_with_namespace:"fe / wsc-tee-goods-common",path:"wsc-goods-common",path_with_namespace:"fe/wsc-goods-common",created_at:"2022-08-16T02:38:25.500Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/wsc-goods-common.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/wsc-goods-common.git",web_url:"https://gitlab.qima-inc.com/fe/wsc-goods-common",readme_url:"https://gitlab.qima-inc.com/fe/wsc-goods-common/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-11T02:13:21.262Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:4946,description:"Iron PC \u57FA\u7840\u4E1A\u52A1\u6846\u67B6",name:"wsc-pc-base",name_with_namespace:"wsc-node / wsc-pc-base",path:"wsc-pc-base",path_with_namespace:"wsc-node/wsc-pc-base",created_at:"2018-06-11T03:03:49.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/wsc-pc-base.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-base.git",web_url:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-base",readme_url:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-base/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:7,last_activity_at:"2025-07-11T00:20:02.191Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:12783,description:"ungoro \u9879\u76EE\u7684\u90E8\u7F72\u4ED3\u5E93",name:"ungoro-dist",name_with_namespace:"fe / ungoro-dist",path:"ungoro-dist",path_with_namespace:"fe/ungoro-dist",created_at:"2021-09-07T11:17:03.847Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/ungoro-dist.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/ungoro-dist.git",web_url:"https://gitlab.qima-inc.com/fe/ungoro-dist",readme_url:null,avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-11T00:10:06.041Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:12680,description:"\u8FDB\u5E97\u670D\u52A1",name:"Enter Shop Service",name_with_namespace:"fe / Enter Shop Service",path:"enter-shop-service",path_with_namespace:"fe/enter-shop-service",created_at:"2021-08-23T07:49:35.270Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/enter-shop-service.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/enter-shop-service.git",web_url:"https://gitlab.qima-inc.com/fe/enter-shop-service",readme_url:"https://gitlab.qima-inc.com/fe/enter-shop-service/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-10T11:33:39.028Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:7388,description:"Iron H5 \u62C6\u5206\u4E1A\u52A1\uFF1A\u5546\u54C1",name:"wsc-h5-goods",name_with_namespace:"wsc-node / wsc-h5-goods",path:"wsc-h5-goods",path_with_namespace:"wsc-node/wsc-h5-goods",created_at:"2019-05-23T06:54:33.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/wsc-h5-goods.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-goods.git",web_url:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-goods",readme_url:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-goods/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:4,last_activity_at:"2025-07-10T11:18:05.942Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:11586,description:"Ranta \u7F16\u8BD1\u65F6\u63D2\u4EF6",name:"ranta-compiler",name_with_namespace:"fe-middle-platform / ranta-compiler",path:"ranta-compiler",path_with_namespace:"fe-middle-platform/ranta-compiler",created_at:"2021-03-02T09:02:43.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe-middle-platform/ranta-compiler.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe-middle-platform/ranta-compiler.git",web_url:"https://gitlab.qima-inc.com/fe-middle-platform/ranta-compiler",readme_url:"https://gitlab.qima-inc.com/fe-middle-platform/ranta-compiler/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:1,last_activity_at:"2025-07-10T09:17:18.137Z",namespace:{id:2081,name:"fe-middle-platform",path:"fe-middle-platform",kind:"group",full_path:"fe-middle-platform",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/2081/images.png",web_url:"https://gitlab.qima-inc.com/groups/fe-middle-platform"}},{id:12139,description:"\u96F6\u552E\u8DE8\u7AEF\u901A\u7528\u4ED3\u5E93",name:"retail-tee-common",name_with_namespace:"retail-web / retail-tee / retail-tee-common",path:"retail-tee-common",path_with_namespace:"retail-web/retail-tee/retail-tee-common",created_at:"2021-05-24T09:09:28.906Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:retail-web/retail-tee/retail-tee-common.git",http_url_to_repo:"https://gitlab.qima-inc.com/retail-web/retail-tee/retail-tee-common.git",web_url:"https://gitlab.qima-inc.com/retail-web/retail-tee/retail-tee-common",readme_url:"https://gitlab.qima-inc.com/retail-web/retail-tee/retail-tee-common/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-10T09:13:16.245Z",namespace:{id:2872,name:"retail-tee",path:"retail-tee",kind:"group",full_path:"retail-web/retail-tee",parent_id:579,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/retail-web/retail-tee"}},{id:7523,description:"\u5C0F\u7A0B\u5E8F\u7BA1\u7406\u5E73\u53F0",name:"weapp-manager",name_with_namespace:"weapp / weapp-manager",path:"weapp-manager",path_with_namespace:"weapp/weapp-manager",created_at:"2019-06-11T13:37:59.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:weapp/weapp-manager.git",http_url_to_repo:"https://gitlab.qima-inc.com/weapp/weapp-manager.git",web_url:"https://gitlab.qima-inc.com/weapp/weapp-manager",readme_url:"https://gitlab.qima-inc.com/weapp/weapp-manager/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:1,last_activity_at:"2025-07-10T08:41:04.150Z",namespace:{id:356,name:"weapp",path:"weapp",kind:"group",full_path:"weapp",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",web_url:"https://gitlab.qima-inc.com/groups/weapp"}},{id:4951,description:"Iron PC \u62C6\u5206\u4E1A\u52A1\uFF1A\u8425\u9500",name:"wsc-pc-ump",name_with_namespace:"wsc-node / wsc-pc-ump",path:"wsc-pc-ump",path_with_namespace:"wsc-node/wsc-pc-ump",created_at:"2018-06-11T03:21:53.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/wsc-pc-ump.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-ump.git",web_url:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-ump",readme_url:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-ump/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:12,last_activity_at:"2025-07-10T08:37:00.038Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:13965,description:"",name:"crm-c-h5-dist",name_with_namespace:"fe / crm-c-h5-dist",path:"crm-c-h5-dist",path_with_namespace:"fe/crm-c-h5-dist",created_at:"2022-03-14T02:23:37.999Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/crm-c-h5-dist.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/crm-c-h5-dist.git",web_url:"https://gitlab.qima-inc.com/fe/crm-c-h5-dist",readme_url:"https://gitlab.qima-inc.com/fe/crm-c-h5-dist/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-10T08:08:23.465Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:15033,description:"",name:"order-domain",name_with_namespace:"retail-web / order-domain",path:"order-domain",path_with_namespace:"retail-web/order-domain",created_at:"2024-08-13T11:13:51.445Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:retail-web/order-domain.git",http_url_to_repo:"https://gitlab.qima-inc.com/retail-web/order-domain.git",web_url:"https://gitlab.qima-inc.com/retail-web/order-domain",readme_url:"https://gitlab.qima-inc.com/retail-web/order-domain/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-10T07:16:27.911Z",namespace:{id:579,name:"retail-web",path:"retail-web",kind:"group",full_path:"retail-web",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/579/box-packing-peanut-512-min.png",web_url:"https://gitlab.qima-inc.com/groups/retail-web"}},{id:10635,description:"Tee \u5927\u6570\u636E\u5B50\u4ED3\u5E93",name:"wsc-tee-statcenter",name_with_namespace:"weapp / wsc-tee-statcenter",path:"wsc-tee-statcenter",path_with_namespace:"weapp/wsc-tee-statcenter",created_at:"2020-09-27T07:43:51.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:weapp/wsc-tee-statcenter.git",http_url_to_repo:"https://gitlab.qima-inc.com/weapp/wsc-tee-statcenter.git",web_url:"https://gitlab.qima-inc.com/weapp/wsc-tee-statcenter",readme_url:"https://gitlab.qima-inc.com/weapp/wsc-tee-statcenter/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-10T06:59:28.701Z",namespace:{id:356,name:"weapp",path:"weapp",kind:"group",full_path:"weapp",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",web_url:"https://gitlab.qima-inc.com/groups/weapp"}},{id:5870,description:"",name:"pc-shared-service",name_with_namespace:"wsc-node / pc-shared-service",path:"pc-shared-service",path_with_namespace:"wsc-node/pc-shared-service",created_at:"2018-10-22T08:44:14.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/pc-shared-service.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/pc-shared-service.git",web_url:"https://gitlab.qima-inc.com/wsc-node/pc-shared-service",readme_url:"https://gitlab.qima-inc.com/wsc-node/pc-shared-service/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:1,last_activity_at:"2025-07-10T06:46:15.853Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:6372,description:"pc-shared-service\u6E90\u7801\u76EE\u5F55",name:"pc-shared-service-source",name_with_namespace:"fe / pc-shared-service-source",path:"pc-shared-service-source",path_with_namespace:"fe/pc-shared-service-source",created_at:"2018-12-26T05:58:06.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/pc-shared-service-source.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/pc-shared-service-source.git",web_url:"https://gitlab.qima-inc.com/fe/pc-shared-service-source",readme_url:"https://gitlab.qima-inc.com/fe/pc-shared-service-source/-/blob/master/README.md",avatar_url:null,forks_count:1,star_count:9,last_activity_at:"2025-07-10T06:38:13.035Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:11447,description:"\u7D20\u6750\u7BA1\u7406",name:"wsc-materials",name_with_namespace:"wsc-node / wsc-materials",path:"wsc-materials",path_with_namespace:"wsc-node/wsc-materials",created_at:"2021-02-03T03:44:53.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/wsc-materials.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/wsc-materials.git",web_url:"https://gitlab.qima-inc.com/wsc-node/wsc-materials",readme_url:"https://gitlab.qima-inc.com/wsc-node/wsc-materials/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:2,last_activity_at:"2025-07-10T02:44:50.407Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:14812,description:"",name:"ext-tee-user",name_with_namespace:"weapp / ext-tee-user",path:"ext-tee-user",path_with_namespace:"weapp/ext-tee-user",created_at:"2023-09-22T09:53:56.573Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:weapp/ext-tee-user.git",http_url_to_repo:"https://gitlab.qima-inc.com/weapp/ext-tee-user.git",web_url:"https://gitlab.qima-inc.com/weapp/ext-tee-user",readme_url:"https://gitlab.qima-inc.com/weapp/ext-tee-user/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-10T02:11:34.029Z",namespace:{id:356,name:"weapp",path:"weapp",kind:"group",full_path:"weapp",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",web_url:"https://gitlab.qima-inc.com/groups/weapp"}},{id:11543,description:"\u57FA\u4E8Ezent\u7684PC\u4E1A\u52A1\u901A\u7528\u4EA4\u4E92\u6A21\u5F0F",name:"zent-pattern",name_with_namespace:"fe / zent-pattern",path:"zent-pattern",path_with_namespace:"fe/zent-pattern",created_at:"2021-02-23T02:13:49.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/zent-pattern.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/zent-pattern.git",web_url:"https://gitlab.qima-inc.com/fe/zent-pattern",readme_url:"https://gitlab.qima-inc.com/fe/zent-pattern/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-09T09:28:49.135Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:4592,description:"Iron H5 \u62C6\u5206\u4E1A\u52A1\uFF1A\u5404\u79CD\u6742\u4E03\u6742\u516B\u4E1A\u52A1",name:"wsc-h5-v3",name_with_namespace:"wsc-node / wsc-h5-v3",path:"wsc-h5-v3",path_with_namespace:"wsc-node/wsc-h5-v3",created_at:"2018-04-19T03:26:13.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/wsc-h5-v3.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-v3.git",web_url:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-v3",readme_url:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-v3/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:1,last_activity_at:"2025-07-09T07:54:46.046Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:9617,description:"\u4E09\u65B9\u6E20\u9053",name:"wsc-pc-channel",name_with_namespace:"wsc-node / wsc-pc-channel",path:"wsc-pc-channel",path_with_namespace:"wsc-node/wsc-pc-channel",created_at:"2020-05-18T02:53:27.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/wsc-pc-channel.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-channel.git",web_url:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-channel",readme_url:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-channel/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-09T07:38:36.417Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:4948,description:"Iron PC \u62C6\u5206\u4E1A\u52A1\uFF1A\u6742\u4E03\u6742\u516B\u4E1A\u52A1\uFF0C\u8BBE\u7F6E",name:"wsc-pc-v4",name_with_namespace:"wsc-node / wsc-pc-v4",path:"wsc-pc-v4",path_with_namespace:"wsc-node/wsc-pc-v4",created_at:"2018-06-11T03:10:04.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/wsc-pc-v4.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-v4.git",web_url:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-v4",readme_url:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-v4/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:7,last_activity_at:"2025-07-09T07:19:06.577Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:12947,description:"",name:"salesman-mono",name_with_namespace:"fe / salesman-mono",path:"salesman-mono",path_with_namespace:"fe/salesman-mono",created_at:"2021-10-08T11:54:41.260Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/salesman-mono.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/salesman-mono.git",web_url:"https://gitlab.qima-inc.com/fe/salesman-mono",readme_url:"https://gitlab.qima-inc.com/fe/salesman-mono/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:1,last_activity_at:"2025-07-09T07:05:20.843Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:11814,description:"",name:"wsc-tee-salesman",name_with_namespace:"weapp / wsc-tee-salesman",path:"wsc-tee-salesman",path_with_namespace:"weapp/wsc-tee-salesman",created_at:"2021-04-01T09:41:17.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:weapp/wsc-tee-salesman.git",http_url_to_repo:"https://gitlab.qima-inc.com/weapp/wsc-tee-salesman.git",web_url:"https://gitlab.qima-inc.com/weapp/wsc-tee-salesman",readme_url:"https://gitlab.qima-inc.com/weapp/wsc-tee-salesman/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:2,last_activity_at:"2025-07-09T07:05:07.522Z",namespace:{id:356,name:"weapp",path:"weapp",kind:"group",full_path:"weapp",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",web_url:"https://gitlab.qima-inc.com/groups/weapp"}},{id:5902,description:"App \u5E94\u7528\u5E02\u573A app.youzanyun.com",name:"app-web",name_with_namespace:"fe-ecloud / app-web",path:"app-web",path_with_namespace:"fe-ecloud/app-web",created_at:"2018-10-29T07:38:04.000Z",tag_list:[],ssh_url_to_repo:"***********************:fe-ecloud/app-web.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe-ecloud/app-web.git",web_url:"https://gitlab.qima-inc.com/fe-ecloud/app-web",readme_url:"https://gitlab.qima-inc.com/fe-ecloud/app-web/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:3,last_activity_at:"2025-07-09T06:14:40.171Z",namespace:{id:1334,name:"fe-ecloud",path:"fe-ecloud",kind:"group",full_path:"fe-ecloud",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/fe-ecloud"}},{id:4700,description:"The distribution of im-node.",name:"im-node-dist",name_with_namespace:"fe / im-node-dist",path:"im-node-dist",path_with_namespace:"fe/im-node-dist",created_at:"2018-05-09T03:03:36.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/im-node-dist.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/im-node-dist.git",web_url:"https://gitlab.qima-inc.com/fe/im-node-dist",readme_url:null,avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-09T06:11:30.514Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:6999,description:"C\u7AEFIM\u53D1\u5E03\u4ED3\u5E93",name:"wap-im-dist",name_with_namespace:"fe / wap-im-dist",path:"wap-im-dist",path_with_namespace:"fe/wap-im-dist",created_at:"2019-04-03T02:04:26.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/wap-im-dist.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/wap-im-dist.git",web_url:"https://gitlab.qima-inc.com/fe/wap-im-dist",readme_url:"https://gitlab.qima-inc.com/fe/wap-im-dist/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-09T03:59:02.419Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:6998,description:"C\u7AEFIM\u6E90\u7801\u4ED3\u5E93",name:"wap-im-web",name_with_namespace:"fe / wap-im-web",path:"wap-im-web",path_with_namespace:"fe/wap-im-web",created_at:"2019-04-03T02:03:49.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/wap-im-web.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/wap-im-web.git",web_url:"https://gitlab.qima-inc.com/fe/wap-im-web",readme_url:"https://gitlab.qima-inc.com/fe/wap-im-web/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:1,last_activity_at:"2025-07-09T03:55:53.901Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:14634,description:"",name:"cloud-ranta-tee",name_with_namespace:"fe-middle-platform / cloud-ranta-tee",path:"cloud-ranta-tee",path_with_namespace:"fe-middle-platform/cloud-ranta-tee",created_at:"2023-05-08T02:48:30.979Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe-middle-platform/cloud-ranta-tee.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe-middle-platform/cloud-ranta-tee.git",web_url:"https://gitlab.qima-inc.com/fe-middle-platform/cloud-ranta-tee",readme_url:"https://gitlab.qima-inc.com/fe-middle-platform/cloud-ranta-tee/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-09T03:43:21.151Z",namespace:{id:2081,name:"fe-middle-platform",path:"fe-middle-platform",kind:"group",full_path:"fe-middle-platform",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/2081/images.png",web_url:"https://gitlab.qima-inc.com/groups/fe-middle-platform"}},{id:12767,description:"CRM \u79DF\u6237\u6807\u5904\u7406",name:"platform-tenant",name_with_namespace:"fe / platform-tenant",path:"platform-tenant",path_with_namespace:"fe/platform-tenant",created_at:"2021-09-06T03:47:34.991Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/platform-tenant.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/platform-tenant.git",web_url:"https://gitlab.qima-inc.com/fe/platform-tenant",readme_url:"https://gitlab.qima-inc.com/fe/platform-tenant/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-09T03:31:35.799Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:10934,description:"\u6709\u8D5E scrm \u5E97\u94FA\u5347\u7EA7\u68C0\u67E5\u63D2\u4EF6",name:"scrm-shop-check-plugin",name_with_namespace:"fe / scrm-shop-check-plugin",path:"scrm-shop-check-plugin",path_with_namespace:"fe/scrm-shop-check-plugin",created_at:"2020-11-20T04:07:04.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/scrm-shop-check-plugin.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/scrm-shop-check-plugin.git",web_url:"https://gitlab.qima-inc.com/fe/scrm-shop-check-plugin",readme_url:"https://gitlab.qima-inc.com/fe/scrm-shop-check-plugin/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-09T03:29:06.187Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:6321,description:"",name:"trash",name_with_namespace:"chenting / trash",path:"trash",path_with_namespace:"chenting/trash",created_at:"2018-12-18T18:23:00.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:chenting/trash.git",http_url_to_repo:"https://gitlab.qima-inc.com/chenting/trash.git",web_url:"https://gitlab.qima-inc.com/chenting/trash",readme_url:"https://gitlab.qima-inc.com/chenting/trash/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-09T02:46:33.795Z",namespace:{id:904,name:"chenting",path:"chenting",kind:"user",full_path:"chenting",parent_id:null,avatar_url:"/uploads/-/system/user/avatar/807/avatar.png",web_url:"https://gitlab.qima-inc.com/chenting"}},{id:9467,description:"",name:"wsc-pc-apps",name_with_namespace:"fe-ecloud / wsc-pc-apps",path:"wsc-pc-apps",path_with_namespace:"fe-ecloud/wsc-pc-apps",created_at:"2020-04-26T11:29:29.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe-ecloud/wsc-pc-apps.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe-ecloud/wsc-pc-apps.git",web_url:"https://gitlab.qima-inc.com/fe-ecloud/wsc-pc-apps",readme_url:"https://gitlab.qima-inc.com/fe-ecloud/wsc-pc-apps/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:2,last_activity_at:"2025-07-09T02:20:03.889Z",namespace:{id:1334,name:"fe-ecloud",path:"fe-ecloud",kind:"group",full_path:"fe-ecloud",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/fe-ecloud"}},{id:14975,description:"",name:"echo-manage",name_with_namespace:"fe / echo-manage",path:"echo-manage",path_with_namespace:"fe/echo-manage",created_at:"2024-05-23T02:31:29.417Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/echo-manage.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/echo-manage.git",web_url:"https://gitlab.qima-inc.com/fe/echo-manage",readme_url:"https://gitlab.qima-inc.com/fe/echo-manage/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:1,last_activity_at:"2025-07-09T02:14:59.731Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:7832,description:"Ranta \u6C99\u7BB1",name:"ranta-sandbox",name_with_namespace:"fe-middle-platform / ranta-sandbox",path:"ranta-sandbox",path_with_namespace:"fe-middle-platform/ranta-sandbox",created_at:"2019-07-24T07:45:37.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe-middle-platform/ranta-sandbox.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe-middle-platform/ranta-sandbox.git",web_url:"https://gitlab.qima-inc.com/fe-middle-platform/ranta-sandbox",readme_url:"https://gitlab.qima-inc.com/fe-middle-platform/ranta-sandbox/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:4,last_activity_at:"2025-07-08T12:03:21.241Z",namespace:{id:2081,name:"fe-middle-platform",path:"fe-middle-platform",kind:"group",full_path:"fe-middle-platform",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/2081/images.png",web_url:"https://gitlab.qima-inc.com/groups/fe-middle-platform"}},{id:11220,description:"\u4F01\u52A9\u6D77\u62A5\u670D\u52A1\u90E8\u7F72\u4ED3\u5E93",name:"weass-snapshot-dist",name_with_namespace:"fe / weass-snapshot-dist",path:"weass-snapshot-dist",path_with_namespace:"fe/weass-snapshot-dist",created_at:"2021-01-04T09:57:23.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/weass-snapshot-dist.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/weass-snapshot-dist.git",web_url:"https://gitlab.qima-inc.com/fe/weass-snapshot-dist",readme_url:null,avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-08T12:02:07.670Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:10756,description:"\u4F01\u4E1A\u5FAE\u4FE1\u52A9\u624BC\u7AEF monorepo",name:"weass-c-mono",name_with_namespace:"fe / weass-c-mono",path:"weass-c-mono",path_with_namespace:"fe/weass-c-mono",created_at:"2020-10-23T11:02:13.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/weass-c-mono.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/weass-c-mono.git",web_url:"https://gitlab.qima-inc.com/fe/weass-c-mono",readme_url:"https://gitlab.qima-inc.com/fe/weass-c-mono/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-08T11:59:35.331Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:10758,description:"\u4F01\u4E1A\u5FAE\u4FE1\u52A9\u624BB\u7AEFH5\u5E94\u7528\u90E8\u7F72\u4ED3\u5E93",name:"weass-b-h5-dist",name_with_namespace:"fe / weass-b-h5-dist",path:"weass-b-h5-dist",path_with_namespace:"fe/weass-b-h5-dist",created_at:"2020-10-23T11:13:14.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/weass-b-h5-dist.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/weass-b-h5-dist.git",web_url:"https://gitlab.qima-inc.com/fe/weass-b-h5-dist",readme_url:null,avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-08T07:15:59.143Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:10967,description:"",name:"garden",name_with_namespace:"fe / garden",path:"garden",path_with_namespace:"fe/garden",created_at:"2020-11-26T03:03:50.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/garden.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/garden.git",web_url:"https://gitlab.qima-inc.com/fe/garden",readme_url:"https://gitlab.qima-inc.com/fe/garden/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:3,last_activity_at:"2025-07-08T07:15:34.331Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:15027,description:"",name:"garden-ipaas",name_with_namespace:"fe / garden-ipaas",path:"garden-ipaas",path_with_namespace:"fe/garden-ipaas",created_at:"2024-08-07T08:12:42.138Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/garden-ipaas.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/garden-ipaas.git",web_url:"https://gitlab.qima-inc.com/fe/garden-ipaas",readme_url:"https://gitlab.qima-inc.com/fe/garden-ipaas/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-08T06:59:56.578Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:12279,description:"",name:"goods-domain",name_with_namespace:"fe / goods-domain",path:"goods-domain",path_with_namespace:"fe/goods-domain",created_at:"2021-06-17T11:40:12.044Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/goods-domain.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/goods-domain.git",web_url:"https://gitlab.qima-inc.com/fe/goods-domain",readme_url:"https://gitlab.qima-inc.com/fe/goods-domain/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:1,last_activity_at:"2025-07-08T06:31:14.943Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:12519,description:"",name:"Taurus Front",name_with_namespace:"fe / Taurus Front",path:"taurus-front",path_with_namespace:"fe/taurus-front",created_at:"2021-07-26T06:09:01.402Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/taurus-front.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/taurus-front.git",web_url:"https://gitlab.qima-inc.com/fe/taurus-front",readme_url:"https://gitlab.qima-inc.com/fe/taurus-front/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-08T03:24:15.309Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:9985,description:"",name:"wsc-h5-statcenter",name_with_namespace:"wsc-node / wsc-h5-statcenter",path:"wsc-h5-statcenter",path_with_namespace:"wsc-node/wsc-h5-statcenter",created_at:"2020-07-06T07:29:26.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/wsc-h5-statcenter.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-statcenter.git",web_url:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-statcenter",readme_url:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-statcenter/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-08T02:18:12.111Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:12843,description:"",name:"garden-gdp",name_with_namespace:"fe / garden-gdp",path:"garden-gdp",path_with_namespace:"fe/garden-gdp",created_at:"2021-09-16T13:32:45.469Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/garden-gdp.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/garden-gdp.git",web_url:"https://gitlab.qima-inc.com/fe/garden-gdp",readme_url:"https://gitlab.qima-inc.com/fe/garden-gdp/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-07T12:25:38.488Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:15402,description:"",name:"test",name_with_namespace:"xujiazheng / test",path:"test",path_with_namespace:"xujiazheng/test",created_at:"2025-06-13T03:23:21.547Z",default_branch:"main",tag_list:[],ssh_url_to_repo:"***********************:xujiazheng/test.git",http_url_to_repo:"https://gitlab.qima-inc.com/xujiazheng/test.git",web_url:"https://gitlab.qima-inc.com/xujiazheng/test",readme_url:null,avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-07T11:54:48.693Z",namespace:{id:3020,name:"xujiazheng",path:"xujiazheng",kind:"user",full_path:"xujiazheng",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/xujiazheng"}},{id:11535,description:"",name:"ui-test",name_with_namespace:"retail-web / ui-test",path:"ui-test",path_with_namespace:"retail-web/ui-test",created_at:"2021-02-22T02:30:40.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:retail-web/ui-test.git",http_url_to_repo:"https://gitlab.qima-inc.com/retail-web/ui-test.git",web_url:"https://gitlab.qima-inc.com/retail-web/ui-test",readme_url:"https://gitlab.qima-inc.com/retail-web/ui-test/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:1,last_activity_at:"2025-07-07T09:35:40.562Z",namespace:{id:579,name:"retail-web",path:"retail-web",kind:"group",full_path:"retail-web",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/579/box-packing-peanut-512-min.png",web_url:"https://gitlab.qima-inc.com/groups/retail-web"}},{id:14833,description:"\u65B0\u96F6\u552E\u8FD0\u8425\u5E73\u53F0",name:"garden-retail",name_with_namespace:"fe / garden-retail",path:"garden-retail",path_with_namespace:"fe/garden-retail",created_at:"2023-10-25T03:19:09.089Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/garden-retail.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/garden-retail.git",web_url:"https://gitlab.qima-inc.com/fe/garden-retail",readme_url:"https://gitlab.qima-inc.com/fe/garden-retail/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-07T07:15:22.540Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:11661,description:"\u8D26\u53F7 - \u591A\u7AEF\u4E2D\u53F0\u5316\u6269\u5C55\u4ED3\u5E93",name:"ext-tee-passport",name_with_namespace:"weapp / ext-tee-passport",path:"ext-tee-passport",path_with_namespace:"weapp/ext-tee-passport",created_at:"2021-03-12T09:37:42.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:weapp/ext-tee-passport.git",http_url_to_repo:"https://gitlab.qima-inc.com/weapp/ext-tee-passport.git",web_url:"https://gitlab.qima-inc.com/weapp/ext-tee-passport",readme_url:"https://gitlab.qima-inc.com/weapp/ext-tee-passport/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:1,last_activity_at:"2025-07-07T03:46:51.226Z",namespace:{id:356,name:"weapp",path:"weapp",kind:"group",full_path:"weapp",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",web_url:"https://gitlab.qima-inc.com/groups/weapp"}},{id:15323,description:"",name:"jira-assistant",name_with_namespace:"fe / jira-assistant",path:"jira-assistant",path_with_namespace:"fe/jira-assistant",created_at:"2025-05-19T06:24:52.694Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/jira-assistant.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/jira-assistant.git",web_url:"https://gitlab.qima-inc.com/fe/jira-assistant",readme_url:"https://gitlab.qima-inc.com/fe/jira-assistant/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-07T02:53:21.086Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:10969,description:"",name:"wsc-h5-aftersales",name_with_namespace:"wsc-node / wsc-h5-aftersales",path:"wsc-h5-aftersales",path_with_namespace:"wsc-node/wsc-h5-aftersales",created_at:"2020-11-26T05:33:00.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/wsc-h5-aftersales.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-aftersales.git",web_url:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-aftersales",readme_url:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-aftersales/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:3,last_activity_at:"2025-07-06T16:00:08.908Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:12261,description:"\u5FAE\u4FE1\u89C6\u9891\u53F7",name:"wsc-pc-wxvideo",name_with_namespace:"wsc-node / wsc-pc-wxvideo",path:"wsc-pc-wxvideo",path_with_namespace:"wsc-node/wsc-pc-wxvideo",created_at:"2021-06-16T03:23:50.794Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/wsc-pc-wxvideo.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-wxvideo.git",web_url:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-wxvideo",readme_url:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-wxvideo/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-04T16:10:36.531Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:4422,description:"\u4F1A\u5458\u4E2D\u5FC3\uFF0C\u6743\u76CA\u5361\u7B49\u4F1A\u5458 C \u7AEF\u4E1A\u52A1",name:"wsc-h5-user",name_with_namespace:"wsc-node / wsc-h5-user",path:"wsc-h5-user",path_with_namespace:"wsc-node/wsc-h5-user",created_at:"2018-03-22T03:07:08.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/wsc-h5-user.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-user.git",web_url:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-user",readme_url:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-user/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:5,last_activity_at:"2025-07-04T16:10:30.802Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:11626,description:"",name:"matrix-manage-node-dist",name_with_namespace:"fe / matrix-manage-node-dist",path:"matrix-manage-node-dist",path_with_namespace:"fe/matrix-manage-node-dist",created_at:"2021-03-08T12:18:27.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/matrix-manage-node-dist.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/matrix-manage-node-dist.git",web_url:"https://gitlab.qima-inc.com/fe/matrix-manage-node-dist",readme_url:"https://gitlab.qima-inc.com/fe/matrix-manage-node-dist/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-04T16:10:29.390Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:11625,description:"",name:"matrix-runtime",name_with_namespace:"fe / matrix-runtime",path:"matrix-runtime",path_with_namespace:"fe/matrix-runtime",created_at:"2021-03-08T12:16:43.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/matrix-runtime.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/matrix-runtime.git",web_url:"https://gitlab.qima-inc.com/fe/matrix-runtime",readme_url:null,avatar_url:null,forks_count:0,star_count:1,last_activity_at:"2025-07-04T16:10:28.781Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:11617,description:"\u4EA7\u54C1\u77E9\u9635\u7BA1\u7406\u540E\u53F0",name:"matrix-manage",name_with_namespace:"fe / matrix-manage",path:"matrix-manage",path_with_namespace:"fe/matrix-manage",created_at:"2021-03-08T03:22:55.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/matrix-manage.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/matrix-manage.git",web_url:"https://gitlab.qima-inc.com/fe/matrix-manage",readme_url:null,avatar_url:null,forks_count:0,star_count:2,last_activity_at:"2025-07-04T16:10:28.346Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:11302,description:"CDP(custom data platform) front end project",name:"cdp-front",name_with_namespace:"fe / cdp-front",path:"cdp-front",path_with_namespace:"fe/cdp-front",created_at:"2021-01-12T01:51:53.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/cdp-front.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/cdp-front.git",web_url:"https://gitlab.qima-inc.com/fe/cdp-front",readme_url:"https://gitlab.qima-inc.com/fe/cdp-front/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:4,last_activity_at:"2025-07-04T16:10:20.140Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:14137,description:"",name:"fe-service-mono",name_with_namespace:"fe / fe-service-mono",path:"fe-service-mono",path_with_namespace:"fe/fe-service-mono",created_at:"2022-04-25T02:27:58.289Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/fe-service-mono.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/fe-service-mono.git",web_url:"https://gitlab.qima-inc.com/fe/fe-service-mono",readme_url:"https://gitlab.qima-inc.com/fe/fe-service-mono/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-04T16:10:18.720Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:11952,description:"\u4F01\u5FAE\u52A9\u624B\u4EA4\u4ED8\u81EA\u52A8\u5316\u5E94\u7528\u90E8\u7F72\u4ED3\u5E93",name:"weass-automation-dist",name_with_namespace:"fe / weass-automation-dist",path:"weass-automation-dist",path_with_namespace:"fe/weass-automation-dist",created_at:"2021-04-22T06:33:23.147Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/weass-automation-dist.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/weass-automation-dist.git",web_url:"https://gitlab.qima-inc.com/fe/weass-automation-dist",readme_url:null,avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-04T16:10:18.226Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:13058,description:"\u4F01\u5FAE\u52A9\u624B\u670D\u52A1\u5546\u81EA\u52A8\u5316 Node \u5E94\u7528",name:"weass-service-provider-automation-dist",name_with_namespace:"fe / weass-service-provider-automation-dist",path:"weass-service-provider-automation-dist",path_with_namespace:"fe/weass-service-provider-automation-dist",created_at:"2021-10-26T07:54:26.228Z",default_branch:"feat/service-automation-configuration",tag_list:[],ssh_url_to_repo:"***********************:fe/weass-service-provider-automation-dist.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/weass-service-provider-automation-dist.git",web_url:"https://gitlab.qima-inc.com/fe/weass-service-provider-automation-dist",readme_url:null,avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-04T16:10:17.763Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:7703,description:"",name:"wsc-fe-pc-decorate-components",name_with_namespace:"wsc-node / wsc-fe-pc-decorate-components",path:"wsc-fe-pc-decorate-components",path_with_namespace:"wsc-node/wsc-fe-pc-decorate-components",created_at:"2019-07-09T02:26:01.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/wsc-fe-pc-decorate-components.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/wsc-fe-pc-decorate-components.git",web_url:"https://gitlab.qima-inc.com/wsc-node/wsc-fe-pc-decorate-components",readme_url:"https://gitlab.qima-inc.com/wsc-node/wsc-fe-pc-decorate-components/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:2,last_activity_at:"2025-07-04T06:57:19.281Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:14843,description:"",name:"cloud-api-types",name_with_namespace:"fe-middle-platform / cloud-api-types",path:"cloud-api-types",path_with_namespace:"fe-middle-platform/cloud-api-types",created_at:"2023-11-07T02:45:26.728Z",default_branch:"main",tag_list:[],ssh_url_to_repo:"***********************:fe-middle-platform/cloud-api-types.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe-middle-platform/cloud-api-types.git",web_url:"https://gitlab.qima-inc.com/fe-middle-platform/cloud-api-types",readme_url:null,avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-04T06:24:10.877Z",namespace:{id:2081,name:"fe-middle-platform",path:"fe-middle-platform",kind:"group",full_path:"fe-middle-platform",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/2081/images.png",web_url:"https://gitlab.qima-inc.com/groups/fe-middle-platform"}},{id:11966,description:"Awesome React Hooks ",name:"react-hooks",name_with_namespace:"fe / react-hooks",path:"react-hooks",path_with_namespace:"fe/react-hooks",created_at:"2021-04-23T08:32:22.015Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/react-hooks.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/react-hooks.git",web_url:"https://gitlab.qima-inc.com/fe/react-hooks",readme_url:"https://gitlab.qima-inc.com/fe/react-hooks/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:1,last_activity_at:"2025-07-04T03:27:01.385Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:11887,description:"",name:"ump-biz-utils",name_with_namespace:"fe / ump-biz-utils",path:"ump-biz-utils",path_with_namespace:"fe/ump-biz-utils",created_at:"2021-04-16T08:34:24.845Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/ump-biz-utils.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/ump-biz-utils.git",web_url:"https://gitlab.qima-inc.com/fe/ump-biz-utils",readme_url:"https://gitlab.qima-inc.com/fe/ump-biz-utils/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-04T02:48:27.151Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:12826,description:"\u89C6\u9891\u53F7H5\u4E1A\u52A1",name:"wsc-h5-wxvideo",name_with_namespace:"wsc-node / wsc-h5-wxvideo",path:"wsc-h5-wxvideo",path_with_namespace:"wsc-node/wsc-h5-wxvideo",created_at:"2021-09-15T06:43:13.254Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/wsc-h5-wxvideo.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-wxvideo.git",web_url:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-wxvideo",readme_url:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-wxvideo/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:1,last_activity_at:"2025-07-04T02:40:36.739Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:2404,description:"\u5206\u9500\u524D\u7AEF\u4ED3\u5E93\uFF0C\u4ECE\u539F\u6765\u7684iron-front clone\u51FA\u6765\u7684\u3002",name:"fenxiao",name_with_namespace:"fe / fenxiao",path:"fenxiao",path_with_namespace:"fe/fenxiao",created_at:"2017-04-26T09:54:50.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/fenxiao.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/fenxiao.git",web_url:"https://gitlab.qima-inc.com/fe/fenxiao",readme_url:"https://gitlab.qima-inc.com/fe/fenxiao/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:2,last_activity_at:"2025-07-03T12:03:03.052Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:11867,description:"",name:"goods-biz-utils",name_with_namespace:"fe / goods-biz-utils",path:"goods-biz-utils",path_with_namespace:"fe/goods-biz-utils",created_at:"2021-04-14T10:25:48.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/goods-biz-utils.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/goods-biz-utils.git",web_url:"https://gitlab.qima-inc.com/fe/goods-biz-utils",readme_url:"https://gitlab.qima-inc.com/fe/goods-biz-utils/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-03T08:29:11.894Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:12055,description:"",name:"Npm Platform Web Dist",name_with_namespace:"fe / Npm Platform Web Dist",path:"*********************",path_with_namespace:"fe/*********************",created_at:"2021-05-10T12:31:03.365Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/*********************.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/*********************.git",web_url:"https://gitlab.qima-inc.com/fe/*********************",readme_url:null,avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-03T08:15:07.059Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:10213,description:"",name:"wsc-tee-goods",name_with_namespace:"weapp / wsc-tee-goods",path:"wsc-tee-goods",path_with_namespace:"weapp/wsc-tee-goods",created_at:"2020-08-04T09:08:23.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:weapp/wsc-tee-goods.git",http_url_to_repo:"https://gitlab.qima-inc.com/weapp/wsc-tee-goods.git",web_url:"https://gitlab.qima-inc.com/weapp/wsc-tee-goods",readme_url:"https://gitlab.qima-inc.com/weapp/wsc-tee-goods/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:2,last_activity_at:"2025-07-03T07:48:35.688Z",namespace:{id:356,name:"weapp",path:"weapp",kind:"group",full_path:"weapp",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",web_url:"https://gitlab.qima-inc.com/groups/weapp"}},{id:11084,description:"",name:"wsc-tee-shop",name_with_namespace:"weapp / wsc-tee-shop",path:"wsc-tee-shop",path_with_namespace:"weapp/wsc-tee-shop",created_at:"2020-12-14T02:52:46.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:weapp/wsc-tee-shop.git",http_url_to_repo:"https://gitlab.qima-inc.com/weapp/wsc-tee-shop.git",web_url:"https://gitlab.qima-inc.com/weapp/wsc-tee-shop",readme_url:"https://gitlab.qima-inc.com/weapp/wsc-tee-shop/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-03T06:55:48.705Z",namespace:{id:356,name:"weapp",path:"weapp",kind:"group",full_path:"weapp",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",web_url:"https://gitlab.qima-inc.com/groups/weapp"}},{id:15044,description:"",name:"ipaas",name_with_namespace:"fe / ipaas",path:"ipaas",path_with_namespace:"fe/ipaas",created_at:"2024-08-23T09:58:54.262Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/ipaas.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/ipaas.git",web_url:"https://gitlab.qima-inc.com/fe/ipaas",readme_url:"https://gitlab.qima-inc.com/fe/ipaas/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-03T06:05:20.720Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:10177,description:"Tee\u7684\u4E1A\u52A1\u751F\u6001",name:"tee-biz",name_with_namespace:"fe-middle-platform / tee-biz",path:"tee-biz",path_with_namespace:"fe-middle-platform/tee-biz",created_at:"2020-07-29T08:55:04.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe-middle-platform/tee-biz.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe-middle-platform/tee-biz.git",web_url:"https://gitlab.qima-inc.com/fe-middle-platform/tee-biz",readme_url:"https://gitlab.qima-inc.com/fe-middle-platform/tee-biz/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:2,last_activity_at:"2025-07-03T03:34:15.702Z",namespace:{id:2081,name:"fe-middle-platform",path:"fe-middle-platform",kind:"group",full_path:"fe-middle-platform",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/2081/images.png",web_url:"https://gitlab.qima-inc.com/groups/fe-middle-platform"}},{id:12835,description:"",name:"ungoro-mono",name_with_namespace:"fe / ungoro-mono",path:"ungoro-mono",path_with_namespace:"fe/ungoro-mono",created_at:"2021-09-15T10:41:55.415Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/ungoro-mono.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/ungoro-mono.git",web_url:"https://gitlab.qima-inc.com/fe/ungoro-mono",readme_url:"https://gitlab.qima-inc.com/fe/ungoro-mono/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-03T02:53:32.101Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:5377,description:"\u9762\u5411\u5546\u5BB6\u7AEF \u2014\u2014 \u5E10\u53F7\u7CFB\u7EDF",name:"wsc-pc-account",name_with_namespace:"wsc-node / wsc-pc-account",path:"wsc-pc-account",path_with_namespace:"wsc-node/wsc-pc-account",created_at:"2018-08-10T03:08:15.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/wsc-pc-account.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-account.git",web_url:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-account",readme_url:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-account/-/blob/master/README.md",avatar_url:null,forks_count:1,star_count:1,last_activity_at:"2025-07-02T16:00:07.466Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:10070,description:"\u5E10\u53F7\u4FA7\u7EDF\u4E00 NPM \u5305\u96C6\u5408",name:"account-plugin",name_with_namespace:"fe / account-plugin",path:"account-plugin",path_with_namespace:"fe/account-plugin",created_at:"2020-07-15T08:52:52.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/account-plugin.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/account-plugin.git",web_url:"https://gitlab.qima-inc.com/fe/account-plugin",readme_url:"https://gitlab.qima-inc.com/fe/account-plugin/-/blob/master/README.md",avatar_url:"https://gitlab.qima-inc.com/uploads/-/system/project/avatar/10070/account-128.jpg",forks_count:0,star_count:1,last_activity_at:"2025-07-02T10:10:42.172Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:5939,description:"\u9762\u5411\u6D88\u8D39\u8005\u7AEF \u2014\u2014 \u5E10\u53F7\u7CFB\u7EDF",name:"wsc-h5-account",name_with_namespace:"wsc-node / wsc-h5-account",path:"wsc-h5-account",path_with_namespace:"wsc-node/wsc-h5-account",created_at:"2018-11-01T08:32:03.000Z",default_branch:"master",tag_list:["H5","\u8D26\u53F7"],ssh_url_to_repo:"***********************:wsc-node/wsc-h5-account.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-account.git",web_url:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-account",readme_url:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-account/-/blob/master/README.md",avatar_url:"https://gitlab.qima-inc.com/uploads/-/system/project/avatar/5939/account-128.jpg",forks_count:0,star_count:0,last_activity_at:"2025-07-02T08:14:59.011Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:15310,description:"\u5C0F\u7EA2\u4E66Demo\u5C55\u793A\u5C0F\u7A0B\u5E8F",name:"demoApp",name_with_namespace:"fe / demoApp",path:"xhsdemo",path_with_namespace:"fe/xhsdemo",created_at:"2025-05-07T06:11:14.776Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/xhsdemo.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/xhsdemo.git",web_url:"https://gitlab.qima-inc.com/fe/xhsdemo",readme_url:"https://gitlab.qima-inc.com/fe/xhsdemo/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:1,last_activity_at:"2025-07-02T07:32:41.544Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:8177,description:"\u884C\u4E1A\u7535\u5546\uFF1A\u9152\u5E97",name:"wsc-pc-industry",name_with_namespace:"wsc-node / wsc-pc-industry",path:"wsc-pc-industry",path_with_namespace:"wsc-node/wsc-pc-industry",created_at:"2019-09-11T08:02:59.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/wsc-pc-industry.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-industry.git",web_url:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-industry",readme_url:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-industry/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-02T03:53:34.294Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:12509,description:"",name:"ext-tee-guide",name_with_namespace:"weapp / ext-tee-guide",path:"ext-tee-guide",path_with_namespace:"weapp/ext-tee-guide",created_at:"2021-07-22T03:45:14.504Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:weapp/ext-tee-guide.git",http_url_to_repo:"https://gitlab.qima-inc.com/weapp/ext-tee-guide.git",web_url:"https://gitlab.qima-inc.com/weapp/ext-tee-guide",readme_url:"https://gitlab.qima-inc.com/weapp/ext-tee-guide/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-01T12:17:53.532Z",namespace:{id:356,name:"weapp",path:"weapp",kind:"group",full_path:"weapp",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",web_url:"https://gitlab.qima-inc.com/groups/weapp"}},{id:15325,description:"",name:"open-webui",name_with_namespace:"fe / open-webui",path:"open-webui",path_with_namespace:"fe/open-webui",created_at:"2025-05-20T10:10:42.115Z",default_branch:"main",tag_list:[],ssh_url_to_repo:"***********************:fe/open-webui.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/open-webui.git",web_url:"https://gitlab.qima-inc.com/fe/open-webui",readme_url:"https://gitlab.qima-inc.com/fe/open-webui/-/blob/main/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-01T08:50:23.498Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:15257,description:"",name:"rpa-proxy",name_with_namespace:"fe-middle-platform / rpa-proxy",path:"rpa-proxy",path_with_namespace:"fe-middle-platform/rpa-proxy",created_at:"2025-03-25T12:01:00.285Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe-middle-platform/rpa-proxy.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe-middle-platform/rpa-proxy.git",web_url:"https://gitlab.qima-inc.com/fe-middle-platform/rpa-proxy",readme_url:"https://gitlab.qima-inc.com/fe-middle-platform/rpa-proxy/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-01T06:15:28.791Z",namespace:{id:2081,name:"fe-middle-platform",path:"fe-middle-platform",kind:"group",full_path:"fe-middle-platform",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/2081/images.png",web_url:"https://gitlab.qima-inc.com/groups/fe-middle-platform"}},{id:11988,description:"\u6D88\u606F - \u591A\u7AEF\u4E2D\u53F0\u5316\u6269\u5C55\u4ED3\u5E93",name:"ext-tee-wsc-im",name_with_namespace:"weapp / ext-tee-wsc-im",path:"ext-tee-wsc-im",path_with_namespace:"weapp/ext-tee-wsc-im",created_at:"2021-04-26T09:55:17.588Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:weapp/ext-tee-wsc-im.git",http_url_to_repo:"https://gitlab.qima-inc.com/weapp/ext-tee-wsc-im.git",web_url:"https://gitlab.qima-inc.com/weapp/ext-tee-wsc-im",readme_url:"https://gitlab.qima-inc.com/weapp/ext-tee-wsc-im/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-07-01T03:52:03.907Z",namespace:{id:356,name:"weapp",path:"weapp",kind:"group",full_path:"weapp",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",web_url:"https://gitlab.qima-inc.com/groups/weapp"}},{id:10215,description:"\u65E9\u671F\u7EAF\u591A\u7AEF\u4ED3\u5E93(\u88ABwsc-tee-base\u4F9D\u8D56)\uFF0C\u88C5\u4FEE\u5B50\u4ED3\u5E93\uFF0C\u76EE\u524D\u4EC5\u7528\u4E8EQQ\u3001\u652F\u4ED8\u5B9D\u5C0F\u7A0B\u5E8F",name:"wsc-tee-decorate",name_with_namespace:"weapp / wsc-tee-decorate",path:"wsc-tee-decorate",path_with_namespace:"weapp/wsc-tee-decorate",created_at:"2020-08-04T09:09:09.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:weapp/wsc-tee-decorate.git",http_url_to_repo:"https://gitlab.qima-inc.com/weapp/wsc-tee-decorate.git",web_url:"https://gitlab.qima-inc.com/weapp/wsc-tee-decorate",readme_url:"https://gitlab.qima-inc.com/weapp/wsc-tee-decorate/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:2,last_activity_at:"2025-07-01T03:14:02.554Z",namespace:{id:356,name:"weapp",path:"weapp",kind:"group",full_path:"weapp",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",web_url:"https://gitlab.qima-inc.com/groups/weapp"}},{id:9950,description:"\u591A\u7AEF Vant \u7EC4\u4EF6\u5E93",name:"vant-tee",name_with_namespace:"fe-middle-platform / vant-tee",path:"vant-tee",path_with_namespace:"fe-middle-platform/vant-tee",created_at:"2020-07-02T03:17:14.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe-middle-platform/vant-tee.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe-middle-platform/vant-tee.git",web_url:"https://gitlab.qima-inc.com/fe-middle-platform/vant-tee",readme_url:"https://gitlab.qima-inc.com/fe-middle-platform/vant-tee/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:4,last_activity_at:"2025-06-30T12:02:46.370Z",namespace:{id:2081,name:"fe-middle-platform",path:"fe-middle-platform",kind:"group",full_path:"fe-middle-platform",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/2081/images.png",web_url:"https://gitlab.qima-inc.com/groups/fe-middle-platform"}},{id:81,description:"\u6709\u8D5E\u5B98\u7F51",name:"intro",name_with_namespace:"fe / intro",path:"intro",path_with_namespace:"fe/intro",created_at:"2016-01-06T06:28:22.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/intro.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/intro.git",web_url:"https://gitlab.qima-inc.com/fe/intro",readme_url:"https://gitlab.qima-inc.com/fe/intro/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:8,last_activity_at:"2025-06-30T09:13:41.153Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:14911,description:"",name:"minishopify-sdk",name_with_namespace:"weapp / minishopify-sdk",path:"minishopify-sdk",path_with_namespace:"weapp/minishopify-sdk",created_at:"2024-02-29T08:28:41.205Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:weapp/minishopify-sdk.git",http_url_to_repo:"https://gitlab.qima-inc.com/weapp/minishopify-sdk.git",web_url:"https://gitlab.qima-inc.com/weapp/minishopify-sdk",readme_url:"https://gitlab.qima-inc.com/weapp/minishopify-sdk/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-06-30T08:28:20.934Z",namespace:{id:356,name:"weapp",path:"weapp",kind:"group",full_path:"weapp",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",web_url:"https://gitlab.qima-inc.com/groups/weapp"}},{id:12780,description:"\u8FDE\u9501B\u7AEFPC\u90E8\u7F72\u4ED3\u5E93",name:"chain-b-pc-dist",name_with_namespace:"wsc-node / chain-b-pc-dist",path:"chain-b-pc-dist",path_with_namespace:"wsc-node/chain-b-pc-dist",created_at:"2021-09-07T07:19:06.127Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/chain-b-pc-dist.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/chain-b-pc-dist.git",web_url:"https://gitlab.qima-inc.com/wsc-node/chain-b-pc-dist",readme_url:null,avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-06-30T07:52:44.592Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:6806,description:"\u8D44\u4EA7\u91D1\u878D\u53D1\u5E03\u9879\u76EE\uFF08\u4E0D\u9700\u8981\u7EF4\u62A4\u3001\u53EA\u7528\u6237\u53D1\u5E03\uFF09",name:"assets-fin",name_with_namespace:"fe / assets-fin",path:"assets-fin",path_with_namespace:"fe/assets-fin",created_at:"2019-03-04T02:54:21.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/assets-fin.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/assets-fin.git",web_url:"https://gitlab.qima-inc.com/fe/assets-fin",readme_url:"https://gitlab.qima-inc.com/fe/assets-fin/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-06-30T07:40:22.308Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:5769,description:"\u9AD8\u6C47\u901A\u5E73\u53F0 \u5783\u573E\u6876",name:"gaohuitong-merchant",name_with_namespace:"fe / gaohuitong-merchant",path:"gaohuitong-merchant",path_with_namespace:"fe/gaohuitong-merchant",created_at:"2018-09-29T08:10:38.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/gaohuitong-merchant.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/gaohuitong-merchant.git",web_url:"https://gitlab.qima-inc.com/fe/gaohuitong-merchant",readme_url:"https://gitlab.qima-inc.com/fe/gaohuitong-merchant/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-06-30T07:39:18.480Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:9024,description:"\u524D\u7AEF\u5DE5\u7A0B\u5316\u4F53\u7CFB koko",name:"koko",name_with_namespace:"fe / koko",path:"koko",path_with_namespace:"fe/koko",created_at:"2020-02-17T03:06:12.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/koko.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/koko.git",web_url:"https://gitlab.qima-inc.com/fe/koko",readme_url:"https://gitlab.qima-inc.com/fe/koko/-/blob/master/README.md",avatar_url:null,forks_count:1,star_count:13,last_activity_at:"2025-06-30T06:48:41.460Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:14711,description:"",name:"youzan-utils",name_with_namespace:"fe / youzan-utils",path:"youzan-utils",path_with_namespace:"fe/youzan-utils",created_at:"2023-06-25T11:23:33.347Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/youzan-utils.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/youzan-utils.git",web_url:"https://gitlab.qima-inc.com/fe/youzan-utils",readme_url:null,avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-06-30T03:54:17.789Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:14942,description:"",name:"diff-pack-core",name_with_namespace:"weapp / diff-pack-core",path:"diff-pack-core",path_with_namespace:"weapp/diff-pack-core",created_at:"2024-04-03T03:56:23.494Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:weapp/diff-pack-core.git",http_url_to_repo:"https://gitlab.qima-inc.com/weapp/diff-pack-core.git",web_url:"https://gitlab.qima-inc.com/weapp/diff-pack-core",readme_url:"https://gitlab.qima-inc.com/weapp/diff-pack-core/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-06-27T09:00:33.612Z",namespace:{id:356,name:"weapp",path:"weapp",kind:"group",full_path:"weapp",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",web_url:"https://gitlab.qima-inc.com/groups/weapp"}},{id:2355,description:"",name:"staticRedirectRules",name_with_namespace:"fe / staticRedirectRules",path:"staticRedirectRules",path_with_namespace:"fe/staticRedirectRules",created_at:"2017-04-19T05:49:51.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/staticRedirectRules.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/staticRedirectRules.git",web_url:"https://gitlab.qima-inc.com/fe/staticRedirectRules",readme_url:"https://gitlab.qima-inc.com/fe/staticRedirectRules/-/blob/master/README.md",avatar_url:null,forks_count:3,star_count:35,last_activity_at:"2025-06-27T03:11:32.011Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:8976,description:"\u7535\u5546\u524D\u7AEF\u5185\u90E8\u5DE5\u5177\u5E73\u53F0",name:"fe-platform",name_with_namespace:"ebiz-web / fe-platform",path:"fe-platform",path_with_namespace:"ebiz-web/fe-platform",created_at:"2020-02-04T13:14:55.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:ebiz-web/fe-platform.git",http_url_to_repo:"https://gitlab.qima-inc.com/ebiz-web/fe-platform.git",web_url:"https://gitlab.qima-inc.com/ebiz-web/fe-platform",readme_url:null,avatar_url:null,forks_count:0,star_count:1,last_activity_at:"2025-06-26T08:19:16.997Z",namespace:{id:723,name:"ebiz-web",path:"ebiz-web",kind:"group",full_path:"ebiz-web",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/723/2_a499d8d8c31983ce1b5d1e45e629e061_con.png",web_url:"https://gitlab.qima-inc.com/groups/ebiz-web"}},{id:15412,description:"",name:"ai-do",name_with_namespace:"fe / ai-do",path:"ai-do",path_with_namespace:"fe/ai-do",created_at:"2025-06-17T13:31:52.969Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/ai-do.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/ai-do.git",web_url:"https://gitlab.qima-inc.com/fe/ai-do",readme_url:null,avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-06-26T04:09:32.859Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:11371,description:"",name:"wsc-h5-shopcore",name_with_namespace:"wsc-node / wsc-h5-shopcore",path:"wsc-h5-shopcore",path_with_namespace:"wsc-node/wsc-h5-shopcore",created_at:"2021-01-22T09:10:02.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/wsc-h5-shopcore.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-shopcore.git",web_url:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-shopcore",readme_url:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-shopcore/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-06-26T01:52:28.994Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:15456,description:"",name:"rpa-service",name_with_namespace:"fe-middle-platform / rpa-service",path:"rpa-service",path_with_namespace:"fe-middle-platform/rpa-service",created_at:"2025-06-25T07:38:32.336Z",default_branch:null,tag_list:[],ssh_url_to_repo:"***********************:fe-middle-platform/rpa-service.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe-middle-platform/rpa-service.git",web_url:"https://gitlab.qima-inc.com/fe-middle-platform/rpa-service",readme_url:null,avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-06-25T07:38:32.336Z",namespace:{id:2081,name:"fe-middle-platform",path:"fe-middle-platform",kind:"group",full_path:"fe-middle-platform",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/2081/images.png",web_url:"https://gitlab.qima-inc.com/groups/fe-middle-platform"}},{id:7490,description:"\u8D44\u4EA7h5\u7AEF\u4E1A\u52A1\u7EC4\u4EF6\u5E93\uFF0C\u72EC\u7ACB\u53D1\u5B50\u5305",name:"assets-h5-components",name_with_namespace:"fe-assets / assets-h5-components",path:"assets-h5-components",path_with_namespace:"fe-assets/assets-h5-components",created_at:"2019-06-04T08:00:57.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe-assets/assets-h5-components.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe-assets/assets-h5-components.git",web_url:"https://gitlab.qima-inc.com/fe-assets/assets-h5-components",readme_url:"https://gitlab.qima-inc.com/fe-assets/assets-h5-components/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:1,last_activity_at:"2025-06-25T07:05:38.772Z",namespace:{id:1442,name:"fe-assets",path:"fe-assets",kind:"group",full_path:"fe-assets",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/fe-assets"}},{id:11624,description:"",name:"matrix-mono",name_with_namespace:"fe / matrix-mono",path:"matrix-mono",path_with_namespace:"fe/matrix-mono",created_at:"2021-03-08T12:16:05.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/matrix-mono.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/matrix-mono.git",web_url:"https://gitlab.qima-inc.com/fe/matrix-mono",readme_url:"https://gitlab.qima-inc.com/fe/matrix-mono/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:1,last_activity_at:"2025-06-25T03:37:55.685Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:6410,description:"",name:"zan-proxy-plugins",name_with_namespace:"fe / zan-proxy-plugins",path:"zan-proxy-plugins",path_with_namespace:"fe/zan-proxy-plugins",created_at:"2019-01-02T07:01:35.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/zan-proxy-plugins.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/zan-proxy-plugins.git",web_url:"https://gitlab.qima-inc.com/fe/zan-proxy-plugins",readme_url:"https://gitlab.qima-inc.com/fe/zan-proxy-plugins/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-06-24T12:51:16.006Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:11915,description:"\u5E97\u94FA - \u591A\u7AEF\u4E2D\u53F0\u5316\u6269\u5C55\u4ED3\u5E93",name:"ext-tee-shop",name_with_namespace:"weapp / ext-tee-shop",path:"ext-tee-shop",path_with_namespace:"weapp/ext-tee-shop",created_at:"2021-04-19T07:24:26.306Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:weapp/ext-tee-shop.git",http_url_to_repo:"https://gitlab.qima-inc.com/weapp/ext-tee-shop.git",web_url:"https://gitlab.qima-inc.com/weapp/ext-tee-shop",readme_url:"https://gitlab.qima-inc.com/weapp/ext-tee-shop/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-06-24T09:55:02.389Z",namespace:{id:356,name:"weapp",path:"weapp",kind:"group",full_path:"weapp",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",web_url:"https://gitlab.qima-inc.com/groups/weapp"}},{id:11645,description:"",name:"wsc-tee-trade-common",name_with_namespace:"weapp / wsc-tee-trade-common",path:"wsc-tee-trade-common",path_with_namespace:"weapp/wsc-tee-trade-common",created_at:"2021-03-11T02:14:43.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:weapp/wsc-tee-trade-common.git",http_url_to_repo:"https://gitlab.qima-inc.com/weapp/wsc-tee-trade-common.git",web_url:"https://gitlab.qima-inc.com/weapp/wsc-tee-trade-common",readme_url:"https://gitlab.qima-inc.com/weapp/wsc-tee-trade-common/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:3,last_activity_at:"2025-06-23T08:32:52.893Z",namespace:{id:356,name:"weapp",path:"weapp",kind:"group",full_path:"weapp",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",web_url:"https://gitlab.qima-inc.com/groups/weapp"}},{id:14374,description:"ranta-configurator v2",name:"ranta-conf",name_with_namespace:"fe-middle-platform / ranta-conf",path:"ranta-conf",path_with_namespace:"fe-middle-platform/ranta-conf",created_at:"2022-10-17T10:41:40.123Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe-middle-platform/ranta-conf.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe-middle-platform/ranta-conf.git",web_url:"https://gitlab.qima-inc.com/fe-middle-platform/ranta-conf",readme_url:"https://gitlab.qima-inc.com/fe-middle-platform/ranta-conf/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-06-19T13:01:08.206Z",namespace:{id:2081,name:"fe-middle-platform",path:"fe-middle-platform",kind:"group",full_path:"fe-middle-platform",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/2081/images.png",web_url:"https://gitlab.qima-inc.com/groups/fe-middle-platform"}},{id:15299,description:"\u7528\u4E8E\u627F\u8F7DAI\u76F8\u5173\u7684\u529F\u80FD\u57FA\u5EFA\u3002",name:"Ai Infrastructure",name_with_namespace:"wsc-node / Ai Infrastructure",path:"ai-infrastructure",path_with_namespace:"wsc-node/ai-infrastructure",created_at:"2025-04-17T01:59:59.410Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/ai-infrastructure.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/ai-infrastructure.git",web_url:"https://gitlab.qima-inc.com/wsc-node/ai-infrastructure",readme_url:"https://gitlab.qima-inc.com/wsc-node/ai-infrastructure/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-06-19T08:32:09.245Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:6693,description:"B\u7AEF\u5546\u4E1A\u5316\u8BA2\u8D2D\u4E1A\u52A1 ",name:"wsc-pc-subscribe",name_with_namespace:"enable-platform-frontend / wsc-pc-subscribe",path:"wsc-pc-subscribe",path_with_namespace:"enable-platform-frontend/wsc-pc-subscribe",created_at:"2019-02-12T07:00:00.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:enable-platform-frontend/wsc-pc-subscribe.git",http_url_to_repo:"https://gitlab.qima-inc.com/enable-platform-frontend/wsc-pc-subscribe.git",web_url:"https://gitlab.qima-inc.com/enable-platform-frontend/wsc-pc-subscribe",readme_url:"https://gitlab.qima-inc.com/enable-platform-frontend/wsc-pc-subscribe/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:4,last_activity_at:"2025-06-19T03:27:41.776Z",namespace:{id:1147,name:"enable-platform-frontend",path:"enable-platform-frontend",kind:"group",full_path:"enable-platform-frontend",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/enable-platform-frontend"}},{id:4574,description:"\u6709\u8D5E\u8D22\u52A1\u7CFB\u7EDF",name:"finance",name_with_namespace:"fe / finance",path:"finance",path_with_namespace:"fe/finance",created_at:"2018-04-17T03:22:01.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/finance.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/finance.git",web_url:"https://gitlab.qima-inc.com/fe/finance",readme_url:"https://gitlab.qima-inc.com/fe/finance/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-06-19T03:27:39.254Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:5692,description:"",name:"assets-finance",name_with_namespace:"fe / assets-finance",path:"assets-finance",path_with_namespace:"fe/assets-finance",created_at:"2018-09-19T07:00:07.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/assets-finance.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/assets-finance.git",web_url:"https://gitlab.qima-inc.com/fe/assets-finance",readme_url:"https://gitlab.qima-inc.com/fe/assets-finance/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-06-19T03:27:36.519Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:259,description:"",name:"tech",name_with_namespace:"fe / tech",path:"tech",path_with_namespace:"fe/tech",created_at:"2016-03-22T03:40:04.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/tech.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/tech.git",web_url:"https://gitlab.qima-inc.com/fe/tech",readme_url:"https://gitlab.qima-inc.com/fe/tech/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-06-19T03:27:35.337Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:8437,description:"",name:"h5-plugins",name_with_namespace:"wsc-node / h5-plugins",path:"h5-plugins",path_with_namespace:"wsc-node/h5-plugins",created_at:"2019-10-29T03:07:33.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/h5-plugins.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/h5-plugins.git",web_url:"https://gitlab.qima-inc.com/wsc-node/h5-plugins",readme_url:"https://gitlab.qima-inc.com/wsc-node/h5-plugins/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:4,last_activity_at:"2025-06-19T02:52:10.501Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:11945,description:"\u4F01\u5FAE\u52A9\u624B\u4EA4\u4ED8\u81EA\u52A8\u5316\u76F8\u5173\u6E90\u7801",name:"Weass Automation Mono",name_with_namespace:"fe / Weass Automation Mono",path:"weass-automation-mono",path_with_namespace:"fe/weass-automation-mono",created_at:"2021-04-21T06:26:07.441Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/weass-automation-mono.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/weass-automation-mono.git",web_url:"https://gitlab.qima-inc.com/fe/weass-automation-mono",readme_url:"https://gitlab.qima-inc.com/fe/weass-automation-mono/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-06-18T06:39:22.738Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:14839,description:"\u5730\u56FE\u670D\u52A1sdk",name:"yzmap-sdk",name_with_namespace:"xujiazheng / yzmap-sdk",path:"yzmap-sdk",path_with_namespace:"xujiazheng/yzmap-sdk",created_at:"2023-10-30T12:52:46.852Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:xujiazheng/yzmap-sdk.git",http_url_to_repo:"https://gitlab.qima-inc.com/xujiazheng/yzmap-sdk.git",web_url:"https://gitlab.qima-inc.com/xujiazheng/yzmap-sdk",readme_url:"https://gitlab.qima-inc.com/xujiazheng/yzmap-sdk/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-06-18T06:26:05.912Z",namespace:{id:3020,name:"xujiazheng",path:"xujiazheng",kind:"user",full_path:"xujiazheng",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/xujiazheng"}},{id:12146,description:"",name:"ranta-config-node-plugin",name_with_namespace:"fe-middle-platform / ranta-config-node-plugin",path:"ranta-config-node-plugin",path_with_namespace:"fe-middle-platform/ranta-config-node-plugin",created_at:"2021-05-25T09:36:36.091Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe-middle-platform/ranta-config-node-plugin.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe-middle-platform/ranta-config-node-plugin.git",web_url:"https://gitlab.qima-inc.com/fe-middle-platform/ranta-config-node-plugin",readme_url:"https://gitlab.qima-inc.com/fe-middle-platform/ranta-config-node-plugin/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-06-18T06:24:56.596Z",namespace:{id:2081,name:"fe-middle-platform",path:"fe-middle-platform",kind:"group",full_path:"fe-middle-platform",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/2081/images.png",web_url:"https://gitlab.qima-inc.com/groups/fe-middle-platform"}},{id:12653,description:"\u4E2D\u53F0\u5316\u57FA\u7840\u5E93",name:"ranta-base-library",name_with_namespace:"fe-middle-platform / ranta-base-library",path:"ranta-base-library",path_with_namespace:"fe-middle-platform/ranta-base-library",created_at:"2021-08-16T12:15:56.440Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe-middle-platform/ranta-base-library.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe-middle-platform/ranta-base-library.git",web_url:"https://gitlab.qima-inc.com/fe-middle-platform/ranta-base-library",readme_url:"https://gitlab.qima-inc.com/fe-middle-platform/ranta-base-library/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-06-16T09:11:31.092Z",namespace:{id:2081,name:"fe-middle-platform",path:"fe-middle-platform",kind:"group",full_path:"fe-middle-platform",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/2081/images.png",web_url:"https://gitlab.qima-inc.com/groups/fe-middle-platform"}},{id:2507,description:"",name:"apidoc",name_with_namespace:"fe / apidoc",path:"apidoc",path_with_namespace:"fe/apidoc",created_at:"2017-05-12T07:41:19.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/apidoc.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/apidoc.git",web_url:"https://gitlab.qima-inc.com/fe/apidoc",readme_url:"https://gitlab.qima-inc.com/fe/apidoc/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-06-16T02:23:21.050Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:6402,description:"",name:"zan-proxy-mac-client",name_with_namespace:"fe / zan-proxy-mac-client",path:"zan-proxy-mac-client",path_with_namespace:"fe/zan-proxy-mac-client",created_at:"2018-12-30T09:28:37.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/zan-proxy-mac-client.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/zan-proxy-mac-client.git",web_url:"https://gitlab.qima-inc.com/fe/zan-proxy-mac-client",readme_url:"https://gitlab.qima-inc.com/fe/zan-proxy-mac-client/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:2,last_activity_at:"2025-06-16T02:10:17.969Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:11026,description:"",name:"data-base-demo",name_with_namespace:"fe / data-base-demo",path:"data-base-demo",path_with_namespace:"fe/data-base-demo",created_at:"2020-12-04T02:39:31.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/data-base-demo.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/data-base-demo.git",web_url:"https://gitlab.qima-inc.com/fe/data-base-demo",readme_url:"https://gitlab.qima-inc.com/fe/data-base-demo/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-06-14T10:45:24.104Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:14808,description:"",name:"Tee User Components",name_with_namespace:"fe / Tee User Components",path:"tee-user-components",path_with_namespace:"fe/tee-user-components",created_at:"2023-09-18T02:20:24.286Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/tee-user-components.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/tee-user-components.git",web_url:"https://gitlab.qima-inc.com/fe/tee-user-components",readme_url:"https://gitlab.qima-inc.com/fe/tee-user-components/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-06-13T08:54:39.735Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:15284,description:"",name:"garden-zanuse",name_with_namespace:"fe / garden-zanuse",path:"garden-zanuse",path_with_namespace:"fe/garden-zanuse",created_at:"2025-04-09T02:39:02.597Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/garden-zanuse.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/garden-zanuse.git",web_url:"https://gitlab.qima-inc.com/fe/garden-zanuse",readme_url:"https://gitlab.qima-inc.com/fe/garden-zanuse/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-06-11T09:55:10.371Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:10821,description:"Inner application manage platform\uFF0C\u5185\u90E8\u5E94\u7528\u7BA1\u7406\u5E73\u53F0",name:"iamp",name_with_namespace:"iamp / iamp",path:"iamp",path_with_namespace:"iamp/iamp",created_at:"2020-11-03T06:51:16.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:iamp/iamp.git",http_url_to_repo:"https://gitlab.qima-inc.com/iamp/iamp.git",web_url:"https://gitlab.qima-inc.com/iamp/iamp",readme_url:"https://gitlab.qima-inc.com/iamp/iamp/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:1,last_activity_at:"2025-06-11T02:33:43.445Z",namespace:{id:2306,name:"iamp",path:"iamp",kind:"group",full_path:"iamp",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/iamp"}},{id:7609,description:"http://fedoc.qima-inc.com/",name:"fe-doc",name_with_namespace:"fe / fe-doc",path:"fe-doc",path_with_namespace:"fe/fe-doc",created_at:"2019-06-25T02:18:02.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/fe-doc.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/fe-doc.git",web_url:"https://gitlab.qima-inc.com/fe/fe-doc",readme_url:"https://gitlab.qima-inc.com/fe/fe-doc/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:4,last_activity_at:"2025-06-11T02:33:42.603Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:6091,description:`\u6709\u8D5E\u8D22\u52A1\u5E73\u53F0 - \u5BF9\u5916\uFF08\u4EE3\u7406\u5546\u548C\u670D\u52A1\u5546\uFF09\r
commercial-finance.youzan.com`,name:"finance-external",name_with_namespace:"fe / finance-external",path:"finance-external",path_with_namespace:"fe/finance-external",created_at:"2018-11-23T06:27:34.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/finance-external.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/finance-external.git",web_url:"https://gitlab.qima-inc.com/fe/finance-external",readme_url:"https://gitlab.qima-inc.com/fe/finance-external/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-06-11T02:33:42.031Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:15021,description:"",name:"garden-ai-sales",name_with_namespace:"fe / garden-ai-sales",path:"garden-ai-sales",path_with_namespace:"fe/garden-ai-sales",created_at:"2024-07-31T02:45:29.639Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/garden-ai-sales.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/garden-ai-sales.git",web_url:"https://gitlab.qima-inc.com/fe/garden-ai-sales",readme_url:"https://gitlab.qima-inc.com/fe/garden-ai-sales/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-06-09T13:05:08.517Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:5654,description:"",name:"retail-shared",name_with_namespace:"retail-web / retail-shared",path:"retail-shared",path_with_namespace:"retail-web/retail-shared",created_at:"2018-09-13T02:01:25.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:retail-web/retail-shared.git",http_url_to_repo:"https://gitlab.qima-inc.com/retail-web/retail-shared.git",web_url:"https://gitlab.qima-inc.com/retail-web/retail-shared",readme_url:"https://gitlab.qima-inc.com/retail-web/retail-shared/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:1,last_activity_at:"2025-06-09T08:47:56.856Z",namespace:{id:579,name:"retail-web",path:"retail-web",kind:"group",full_path:"retail-web",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/579/box-packing-peanut-512-min.png",web_url:"https://gitlab.qima-inc.com/groups/retail-web"}},{id:7056,description:"",name:"youzan-security",name_with_namespace:"wsc-node / youzan-security",path:"youzan-security",path_with_namespace:"wsc-node/youzan-security",created_at:"2019-04-12T08:02:34.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/youzan-security.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/youzan-security.git",web_url:"https://gitlab.qima-inc.com/wsc-node/youzan-security",readme_url:"https://gitlab.qima-inc.com/wsc-node/youzan-security/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-06-05T09:02:12.574Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:6266,description:"\u6709\u8D5E\u7CBE\u8D5E\u62A5\u540D\u7CFB\u7EDF\u3001\u6709\u8D5E\u7CBE\u9009\u751F\u6D3B\u53F7",name:"wsc-pc-mars",name_with_namespace:"wsc-node / wsc-pc-mars",path:"wsc-pc-mars",path_with_namespace:"wsc-node/wsc-pc-mars",created_at:"2018-12-12T07:17:37.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/wsc-pc-mars.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-mars.git",web_url:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-mars",readme_url:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-mars/-/blob/master/README.md",avatar_url:"https://gitlab.qima-inc.com/uploads/-/system/project/avatar/6266/basicprofile.jpeg",forks_count:0,star_count:1,last_activity_at:"2025-06-04T10:07:56.518Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:7476,description:"\u4E09\u65B9\u6E20\u9053\u63A5\u5165\u7EDF\u4E00\u7F51\u5173\uFF0C\u50CF\u5FAE\u4FE1\u3001\u5FAE\u535A\u3001\u5FEB\u624B\u3001\u767E\u5EA6\u3001\u864E\u7259\u3001\u964C\u964C\u3001\u77E5\u4E4E\u7B49\u5404\u4E09\u65B9\u5E73\u53F0",name:"channel-gateway",name_with_namespace:"wsc-node / channel-gateway",path:"channel-gateway",path_with_namespace:"wsc-node/channel-gateway",created_at:"2019-06-03T08:30:42.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/channel-gateway.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/channel-gateway.git",web_url:"https://gitlab.qima-inc.com/wsc-node/channel-gateway",readme_url:"https://gitlab.qima-inc.com/wsc-node/channel-gateway/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:1,last_activity_at:"2025-06-04T06:55:00.859Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:6038,description:"\u6709\u8D5E JSBridge \u9002\u914D\u5C42\uFF08Zan Native Bridge\uFF09",name:"ZNB",name_with_namespace:"fe / ZNB",path:"ZNB",path_with_namespace:"fe/ZNB",created_at:"2018-11-15T08:09:38.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/ZNB.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/ZNB.git",web_url:"https://gitlab.qima-inc.com/fe/ZNB",readme_url:"https://gitlab.qima-inc.com/fe/ZNB/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:4,last_activity_at:"2025-06-04T02:05:44.401Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:12214,description:"\u4E0B\u5355\u9875\u50A8\u503C\u7EC4\u4EF6\u591A\u7AEF",name:"ext-tee-retail-prepaid",name_with_namespace:"weapp / ext-tee-retail-prepaid",path:"ext-tee-retail-prepaid",path_with_namespace:"weapp/ext-tee-retail-prepaid",created_at:"2021-06-07T03:16:53.920Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:weapp/ext-tee-retail-prepaid.git",http_url_to_repo:"https://gitlab.qima-inc.com/weapp/ext-tee-retail-prepaid.git",web_url:"https://gitlab.qima-inc.com/weapp/ext-tee-retail-prepaid",readme_url:"https://gitlab.qima-inc.com/weapp/ext-tee-retail-prepaid/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:1,last_activity_at:"2025-06-03T08:14:33.478Z",namespace:{id:356,name:"weapp",path:"weapp",kind:"group",full_path:"weapp",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",web_url:"https://gitlab.qima-inc.com/groups/weapp"}},{id:4980,description:"",name:"weapp-log-sdk",name_with_namespace:"fe / weapp-log-sdk",path:"weapp-log-sdk",path_with_namespace:"fe/weapp-log-sdk",created_at:"2018-06-14T11:44:06.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/weapp-log-sdk.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/weapp-log-sdk.git",web_url:"https://gitlab.qima-inc.com/fe/weapp-log-sdk",readme_url:"https://gitlab.qima-inc.com/fe/weapp-log-sdk/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-06-03T07:20:59.974Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:15190,description:"AI\u76F8\u5173\u7684\u6742\u9879\u5185\u5BB9\uFF0C\u811A\u672C\u4EC0\u4E48\u7684",name:"ai_things",name_with_namespace:"mobile / ai_things",path:"ai_things",path_with_namespace:"mobile/ai_things",created_at:"2025-02-24T03:41:20.339Z",default_branch:"main",tag_list:[],ssh_url_to_repo:"***********************:mobile/ai_things.git",http_url_to_repo:"https://gitlab.qima-inc.com/mobile/ai_things.git",web_url:"https://gitlab.qima-inc.com/mobile/ai_things",readme_url:"https://gitlab.qima-inc.com/mobile/ai_things/-/blob/main/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-06-03T06:37:07.682Z",namespace:{id:349,name:"mobile",path:"mobile",kind:"group",full_path:"mobile",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/mobile"}},{id:5196,description:"wsc-h5\u516C\u7528\u4EE3\u7801",name:"wsc-fe-h5-shared",name_with_namespace:"wsc-node / wsc-fe-h5-shared",path:"wsc-fe-h5-shared",path_with_namespace:"wsc-node/wsc-fe-h5-shared",created_at:"2018-07-18T09:40:40.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/wsc-fe-h5-shared.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/wsc-fe-h5-shared.git",web_url:"https://gitlab.qima-inc.com/wsc-node/wsc-fe-h5-shared",readme_url:"https://gitlab.qima-inc.com/wsc-node/wsc-fe-h5-shared/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:5,last_activity_at:"2025-05-29T02:20:23.811Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:5883,description:"\u9762\u5411\u6D88\u8D39\u8005\u7AEF \u2014\u2014 \u524D\u7AEF\u7EC4\u4EF6\u5316\u5BB9\u5668",name:"wsc-h5-components",name_with_namespace:"wsc-node / wsc-h5-components",path:"wsc-h5-components",path_with_namespace:"wsc-node/wsc-h5-components",created_at:"2018-10-25T03:09:56.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/wsc-h5-components.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-components.git",web_url:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-components",readme_url:"https://gitlab.qima-inc.com/wsc-node/wsc-h5-components/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:9,last_activity_at:"2025-05-28T07:43:11.477Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}},{id:13238,description:"\u5BFC\u8D2D\u524D\u7AEFmono\u4ED3\u5E93",name:"guide-mono",name_with_namespace:"fe / guide-mono",path:"guide-mono",path_with_namespace:"fe/guide-mono",created_at:"2021-11-17T07:09:49.603Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/guide-mono.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/guide-mono.git",web_url:"https://gitlab.qima-inc.com/fe/guide-mono",readme_url:"https://gitlab.qima-inc.com/fe/guide-mono/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-05-28T02:40:07.388Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:4626,description:"",name:"retail-node-base",name_with_namespace:"retail-web / retail-node-base",path:"retail-node-base",path_with_namespace:"retail-web/retail-node-base",created_at:"2018-04-24T10:11:51.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:retail-web/retail-node-base.git",http_url_to_repo:"https://gitlab.qima-inc.com/retail-web/retail-node-base.git",web_url:"https://gitlab.qima-inc.com/retail-web/retail-node-base",readme_url:"https://gitlab.qima-inc.com/retail-web/retail-node-base/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:4,last_activity_at:"2025-05-27T11:34:39.033Z",namespace:{id:579,name:"retail-web",path:"retail-web",kind:"group",full_path:"retail-web",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/579/box-packing-peanut-512-min.png",web_url:"https://gitlab.qima-inc.com/groups/retail-web"}},{id:3688,description:"\u96F6\u552E\u524D\u7AEF React \u4E1A\u52A1\u7EC4\u4EF6\u5E93",name:"retail-components",name_with_namespace:"retail-web / retail-components",path:"retail-components",path_with_namespace:"retail-web/retail-components",created_at:"2017-11-01T02:09:55.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:retail-web/retail-components.git",http_url_to_repo:"https://gitlab.qima-inc.com/retail-web/retail-components.git",web_url:"https://gitlab.qima-inc.com/retail-web/retail-components",readme_url:"https://gitlab.qima-inc.com/retail-web/retail-components/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:8,last_activity_at:"2025-05-27T03:00:22.836Z",namespace:{id:579,name:"retail-web",path:"retail-web",kind:"group",full_path:"retail-web",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/579/box-packing-peanut-512-min.png",web_url:"https://gitlab.qima-inc.com/groups/retail-web"}},{id:11458,description:"\u6D77\u62A5\u6E32\u67D3\u670D\u52A1\u65B0\u4ED3\u5E93",name:"youzan-poster",name_with_namespace:"fe / youzan-poster",path:"youzan-poster",path_with_namespace:"fe/youzan-poster",created_at:"2021-02-04T03:04:10.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe/youzan-poster.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe/youzan-poster.git",web_url:"https://gitlab.qima-inc.com/fe/youzan-poster",readme_url:"https://gitlab.qima-inc.com/fe/youzan-poster/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-05-22T08:38:19.643Z",namespace:{id:25,name:"fe",path:"fe",kind:"group",full_path:"fe",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/25/html-css-js.png",web_url:"https://gitlab.qima-inc.com/groups/fe"}},{id:6646,description:"\u8D44\u4EA7\u4E1A\u52A1\u7EC4\u4EF6\u5E93",name:"assets-components",name_with_namespace:"fe-assets / assets-components",path:"assets-components",path_with_namespace:"fe-assets/assets-components",created_at:"2019-01-30T02:32:20.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:fe-assets/assets-components.git",http_url_to_repo:"https://gitlab.qima-inc.com/fe-assets/assets-components.git",web_url:"https://gitlab.qima-inc.com/fe-assets/assets-components",readme_url:"https://gitlab.qima-inc.com/fe-assets/assets-components/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:0,last_activity_at:"2025-05-22T06:36:52.384Z",namespace:{id:1442,name:"fe-assets",path:"fe-assets",kind:"group",full_path:"fe-assets",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/fe-assets"}},{id:4260,description:"",name:"wsc-utils",name_with_namespace:"weapp / wsc-utils",path:"wsc-utils",path_with_namespace:"weapp/wsc-utils",created_at:"2018-02-20T08:37:01.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:weapp/wsc-utils.git",http_url_to_repo:"https://gitlab.qima-inc.com/weapp/wsc-utils.git",web_url:"https://gitlab.qima-inc.com/weapp/wsc-utils",readme_url:"https://gitlab.qima-inc.com/weapp/wsc-utils/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:4,last_activity_at:"2025-05-21T03:51:49.799Z",namespace:{id:356,name:"weapp",path:"weapp",kind:"group",full_path:"weapp",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",web_url:"https://gitlab.qima-inc.com/groups/weapp"}},{id:11928,description:"\u8425\u9500 - \u591A\u7AEF\u4E2D\u53F0\u5316\u6269\u5C55\u4ED3\u5E93",name:"ext-tee-wsc-ump",name_with_namespace:"weapp / ext-tee-wsc-ump",path:"ext-tee-wsc-ump",path_with_namespace:"weapp/ext-tee-wsc-ump",created_at:"2021-04-20T06:40:33.483Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:weapp/ext-tee-wsc-ump.git",http_url_to_repo:"https://gitlab.qima-inc.com/weapp/ext-tee-wsc-ump.git",web_url:"https://gitlab.qima-inc.com/weapp/ext-tee-wsc-ump",readme_url:"https://gitlab.qima-inc.com/weapp/ext-tee-wsc-ump/-/blob/master/README.md",avatar_url:null,forks_count:0,star_count:1,last_activity_at:"2025-05-20T09:12:57.994Z",namespace:{id:356,name:"weapp",path:"weapp",kind:"group",full_path:"weapp",parent_id:null,avatar_url:"/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",web_url:"https://gitlab.qima-inc.com/groups/weapp"}},{id:8763,description:"\u589E\u957F\u4E2D\u5FC3\u5E7F\u544A\u3001CPS\u76F8\u5173\u4E1A\u52A1",name:"wsc-pc-cps",name_with_namespace:"wsc-node / wsc-pc-cps",path:"wsc-pc-cps",path_with_namespace:"wsc-node/wsc-pc-cps",created_at:"2019-12-20T02:59:51.000Z",default_branch:"master",tag_list:[],ssh_url_to_repo:"***********************:wsc-node/wsc-pc-cps.git",http_url_to_repo:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-cps.git",web_url:"https://gitlab.qima-inc.com/wsc-node/wsc-pc-cps",readme_url:null,avatar_url:null,forks_count:0,star_count:2,last_activity_at:"2025-05-16T16:13:02.977Z",namespace:{id:1065,name:"wsc-node",path:"wsc-node",kind:"group",full_path:"wsc-node",parent_id:null,avatar_url:null,web_url:"https://gitlab.qima-inc.com/groups/wsc-node"}}];function Y(){return V({appName:"wsc",search:"^hotfix/v2.2"}).then(e=>e.sort((a,i)=>+new Date(i.commit.created_at)-+new Date(a.commit.created_at))).then(e=>e[0].name)}function tt(e){return K({appName:e}).then(a=>a.ssh_url_to_repo)}function v(e){return X.find(a=>a.name===e)?.id}function at({appName:e,filePath:a,branch:i}){return Q({appName:e,filePath:a,branch:i}).then(s=>Buffer.from(s.content,"base64").toString())}async function et(e){let{appName:a,branchName:i,subApps:s}=e;if(!a||!i||!Array.isArray(s))throw new Error("appName, branchName\u548CsubApps\u90FD\u662F\u5FC5\u586B\u53C2\u6570");return at({appName:a,filePath:"koko.repo.json",branch:i}).then(r=>{let c=JSON.parse(r);return c.repos.forEach(n=>{s.includes(n.name)&&(n.branch=i)}),A({appName:a,filePath:"koko.repo.json",branch:i,content:JSON.stringify(c,null,2),commit_message:`feat: \u66F4\u65B0${s}\u7684\u5206\u652F\u4E3A${i}`})})}async function it({appName:e,branchName:a,path:i,packageInfo:s}){let r=i?`${i}/package.json`:"package.json";return at({appName:e,filePath:r,branch:a}).then(c=>{let n=JSON.parse(c),{name:o,version:p}=s;return n?.dependencies?.[o]&&(n.dependencies[o]=p),n?.devDependencies?.[o]&&(n.devDependencies[o]=p),n?.peerDependencies?.[o]&&(n.peerDependencies[o]=p),n?.resolutions?.[o]&&(n.resolutions[o]=p),A({appName:e,filePath:r,branch:a,content:JSON.stringify(n,null,2),commit_message:`feat: \u66F4\u65B0${o}\u7684\u4F9D\u8D56\u7248\u672C\u4E3A${p}`})})}var st=process.env.app_dir||M.join(J,"../github");function Tt(){try{return d("git branch --show-current",{encoding:"utf8"}).trim()}catch{return"unknown"}}function Et(){try{return!d("git status --porcelain",{encoding:"utf8"}).trim()}catch{return!1}}async function Zt(e,a=st){let i=M.join(a,e);if(E.existsSync(i))return t.log(`\u2713 Project directory already exists: ${i}`),i;let s;try{if(s=await tt(e),!s)throw new Error(`Failed to get repository URL for app '${e}'`)}catch(r){throw new Error(`Failed to get git repository URL for app '${e}': ${r.message}`)}t.log(`Project directory not found, cloning from: ${s}`),E.existsSync(a)||(E.mkdirSync(a,{recursive:!0}),t.log(`Created base directory: ${a}`));try{return d(`git clone ${s} ${i}`,{stdio:"inherit"}),t.log(`\u2705 Successfully cloned project to: ${i}`),i}catch(r){throw new Error(`Failed to clone project: ${r.message}`)}}async function rt(e){let{appName:a,baseGithubDir:i=st,branchName:s,baseBranch:r="master",cleanWorkingDir:c=!0,updatePackages:n=!1,packageName:o,packageVersion:p,commitAndPush:u=!0,commitMessage:_}=e;if(!a||!s)throw new Error("appName and branchName are required");let l=j[a]||"";if(l===void 0)throw new Error(`Client directory mapping not found for app '${a}'. Available apps: ${Object.keys(j).join(", ")}`);let g=await Zt(a,i),m={success:!1,currentBranch:null,targetBranch:s,operations:[]};try{process.chdir(g),t.log(`Changed directory to: ${g}`),m.operations.push(`Changed to directory: ${g}`),d("git rev-parse --is-inside-work-tree",{stdio:"ignore"});let q=Tt();m.currentBranch=q,t.log(`Current branch: ${q}`),t.log(`Target branch: ${s}`),t.log(`\u{1F50D} Checking if remote branch '${s}' exists...`);let B=!1;try{await G({appName:a,branchName:s}),B=!0,t.log(`\u2713 Remote branch '${s}' exists`),m.operations.push(`Remote branch exists: ${s}`)}catch{t.log(`Remote branch '${s}' does not exist`),m.operations.push(`Remote branch does not exist: ${s}`)}if(!B){t.log(`\u{1F680} Creating remote branch '${s}' from '${r}'...`);try{await T({appName:a,branchName:s,targetBranchName:r}),t.log(`\u2705 Successfully created remote branch '${s}'`),m.operations.push(`Created remote branch: ${s} from ${r}`)}catch(h){throw new Error(`Failed to create remote branch: ${h.message}`)}}if(n){if(t.log(`
\u{1F504} Starting npm package update via API...`),!o||!p)throw new Error("packageName and packageVersion are required when updatePackages is true");t.log("\u{1F4E6} Updating package.json via API...");try{await it({appName:a,branchName:s,path:l||void 0,packageInfo:{name:o,version:p}}),t.log(`\u2705 Successfully updated ${o} to ${p} via API`),m.operations.push(`Updated package via API: ${o}@${p}`)}catch(h){throw new Error(`Failed to update package.json via API: ${h.message}`)}}t.log(`
\u{1F504} Starting local operations...`);let f=l?M.join(g,l):g;if(q===s)if(t.log(`\u2713 Already on branch '${s}'`),Et())t.log("Working directory is clean, pulling latest changes..."),d(`git pull origin ${s}`,{stdio:"inherit"}),m.operations.push(`Pulled latest changes from origin/${s}`);else if(c){t.log("Working directory has changes, cleaning up first...");let h=d("git status --porcelain",{encoding:"utf8"});h.trim()&&(t.log("Found uncommitted changes:"),t.log(h),t.log("Cleaning up uncommitted changes..."),d("git reset --hard HEAD",{stdio:"inherit"}),t.log("\u2713 Reset all changes to last commit"),m.operations.push("Reset uncommitted changes"),d("git clean -fd",{stdio:"inherit"}),t.log("\u2713 Removed untracked files and directories"),m.operations.push("Removed untracked files")),t.log("Pulling latest changes..."),d(`git pull origin ${s}`,{stdio:"inherit"}),m.operations.push(`Pulled latest changes from origin/${s}`)}else t.log("Working directory has changes, skipping pull (cleanWorkingDir=false)"),m.operations.push("Skipped pull due to uncommitted changes");else{if(c){t.log("Checking for uncommitted changes...");try{let h=d("git status --porcelain",{encoding:"utf8"});h.trim()?(t.log("Found uncommitted changes:"),t.log(h),t.log("Cleaning up uncommitted changes..."),d("git reset --hard HEAD",{stdio:"inherit"}),t.log("\u2713 Reset all changes to last commit"),m.operations.push("Reset uncommitted changes"),d("git clean -fd",{stdio:"inherit"}),t.log("\u2713 Removed untracked files and directories"),m.operations.push("Removed untracked files"),t.log("\u2713 Working directory is now clean")):t.log("\u2713 Working directory is already clean")}catch{t.log("Warning: Could not check git status, continuing...")}}t.log("\u{1F4E5} Fetching latest changes..."),d("git fetch",{stdio:"inherit"}),m.operations.push("Fetched latest changes"),t.log(`\u{1F504} Switching to branch '${s}'...`);try{d(`git checkout ${s}`,{stdio:"inherit"}),m.operations.push(`Checked out branch: ${s}`),t.log("Pulling latest changes..."),d(`git pull origin ${s}`,{stdio:"inherit"}),m.operations.push(`Pulled latest changes from origin/${s}`)}catch{t.log(`Local branch doesn't exist, creating from origin/${s}...`),d(`git checkout -b ${s} origin/${s}`,{stdio:"inherit"}),m.operations.push(`Created local branch from origin/${s}`)}}if(t.log(`
\u{1F4E6} Installing dependencies...`),!E.existsSync(f))throw new Error(`Working directory not found: ${f}`);if(t.log(`Entering working directory: ${f}`),process.chdir(f),m.operations.push(`Changed to working directory: ${f}`),t.log(`\u{1F4E6} Running yarn install in: ${f}`),d("yarn install --non-interactive",{stdio:"inherit"}),m.operations.push(`Executed yarn install in: ${f}`),t.log("\u2705 Dependencies installed successfully!"),n&&(t.log(`
\u{1F4E6} Re-running yarn install to update yarn.lock after API update...`),process.chdir(f),d("yarn install --non-interactive",{stdio:"inherit"}),m.operations.push(`Re-executed yarn install in: ${f}`),t.log("\u2705 yarn.lock updated successfully!")),n&&u){t.log(`
\u{1F4E4} Pushing local changes...`),process.chdir(g),t.log("\u{1F4E5} Fetching latest changes from remote..."),d("git fetch",{stdio:"inherit"}),d(`git pull origin ${s}`,{stdio:"inherit"}),m.operations.push("Pulled latest changes including API updates");let h=l?`${l}/yarn.lock`:"yarn.lock";try{if(d(`git add ${h}`,{stdio:"inherit"}),t.log("Added yarn.lock to git"),m.operations.push("Added yarn.lock to git"),d("git status --porcelain",{encoding:"utf8"}).trim()){let x=_||`chore: update yarn.lock for ${o}@${p}`;d(`git commit -m "${x}"`,{stdio:"inherit"}),t.log(`Committed yarn.lock changes: ${x}`),m.operations.push(`Committed: ${x}`),d(`git push origin ${s}`,{stdio:"inherit"}),t.log(`\u2705 Successfully pushed yarn.lock to origin/${s}`),m.operations.push(`Pushed yarn.lock to origin/${s}`)}else t.log("No yarn.lock changes to commit"),m.operations.push("No yarn.lock changes to commit")}catch{t.log("Warning: Could not add yarn.lock or no yarn.lock file found"),m.operations.push("Warning: yarn.lock handling failed")}}return m.success=!0,m}catch(q){throw m.error=q.message,q}}async function D(e,a){let{modules:i,branch_name:s}=e;if(s==="master")return t.log("Skipping master branch"),{success:!1,error:"Master branch not allowed"};if(!i||!Array.isArray(i)||i.length===0)return t.log("No modules found in data"),{success:!1,error:"No modules found"};t.log(`Processing ${i.length} modules...`);let r=[];try{for(let o of i){let{module_name:p,version:u}=o;if(t.log(`
\u{1F4E6} Processing module: ${p}@${u}`),!a[p]){t.log(`\u23ED\uFE0F  Module ${p} not found in custom app maps, skipping...`);continue}let _=a[p];t.log(`\u{1F3AF} Found ${_.length} target apps: ${_.join(", ")}`);for(let l of _){t.log(`
\u{1F680} Processing app: ${l}`);let g={appName:l,branchName:s,updatePackages:!0,packageName:p,packageVersion:u,clientDir:"client",commitAndPush:!0,commitMessage:`feat: update ${p} to ${u}`};try{let m=await rt(g);t.log(`\u2705 Successfully processed ${l} for ${p}`),r.push({module_name:p,version:u,appName:l,success:!0,operations:m.operations})}catch(m){console.error(`\u274C Failed to process ${l} for ${p}: ${m.message}`),r.push({module_name:p,version:u,appName:l,success:!1,error:m.message})}}}let c=r.filter(o=>!o.success),n=r.filter(o=>o.success).length;return t.log(`
\u{1F4CA} Summary: ${n}/${r.length} operations completed successfully`),c.length>0&&(t.log("\u274C Failed operations:"),c.forEach(o=>{t.log(`   - ${o.appName} (${o.module_name}): ${o.error}`)})),{success:c.length===0,totalModules:i.length,processedOperations:r.length,successfulOperations:n,failedOperations:c.length,results:r}}catch(c){return console.error(`\u274C Unexpected error during processing: ${c.message}`),{success:!1,error:c.message,results:r}}}import xt from"https";import Rt from"http";import{URL as jt}from"url";function At(e,a={}){return new Promise((i,s)=>{let r=new jt(e),{method:c="GET",headers:n={},body:o}=a,p={hostname:r.hostname,port:r.port||(r.protocol==="https:"?443:80),path:r.pathname+r.search,method:c.toUpperCase(),headers:{"User-Agent":"Node.js",...n}};if(o){let l=Buffer.from(o,"utf8");p.headers["Content-Length"]=l.length}let _=(r.protocol==="https:"?xt:Rt).request(p,l=>{let g="";l.on("data",m=>{g+=m}),l.on("end",()=>{let m={ok:l.statusCode>=200&&l.statusCode<300,status:l.statusCode,statusText:l.statusMessage,headers:l.headers,text:()=>Promise.resolve(g),json:()=>{try{return Promise.resolve(JSON.parse(g))}catch{return Promise.reject(new Error("Invalid JSON response"))}}};i(m)})});_.on("error",l=>{s(l)}),o&&_.write(o),_.end()})}var Mt="https://open.feishu.cn/open-apis/bot/v2/hook/4306103e-2dfe-4427-9339-42b87d41aee3";function k(e){let a=process.env.webhook_url||Mt;return a?At(a,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}):Promise.resolve()}import{execSync as Dt}from"child_process";function $t(){try{return Dt("git config user.name",{encoding:"utf8"}).trim()}catch(e){return console.warn("Failed to get git user name:",e.message),"Unknown User"}}function nt(e,a){let{branch_name:i}=a,s=$t();if(!e.success&&(!e.results||e.results.length===0))return`\u274C NPM\u5305\u66F4\u65B0\u5931\u8D25
\u7528\u6237: ${s}
\u5206\u652F: ${i}
\u9519\u8BEF: ${e.error||"\u672A\u77E5\u9519\u8BEF"}`;let{successfulOperations:r,failedOperations:c,results:n}=e,o=n.filter(l=>l.success),p=n.filter(l=>!l.success),_=`${e.success?"\u2705 NPM\u5305\u66F4\u65B0\u5B8C\u6210":"\u26A0\uFE0F NPM\u5305\u66F4\u65B0\u90E8\u5206\u5B8C\u6210"}
`;return _+=`\u7528\u6237: ${s}
`,_+=`\u5206\u652F: ${i}
`,_+=`\u603B\u8BA1: ${r}\u6210\u529F / ${c}\u5931\u8D25

`,o.length>0&&(_+=`\u{1F4E6} \u6210\u529F\u66F4\u65B0\u7684\u5305:
`,o.forEach(l=>{_+=`  \u2022 ${l.module_name}@${l.version} \u2192 ${l.appName}
`})),p.length>0&&(_+=`
\u274C \u5931\u8D25\u7684\u66F4\u65B0:
`,p.forEach(l=>{_+=`  \u2022 ${l.module_name}@${l.version} \u2192 ${l.appName}
`,_+=`    \u9519\u8BEF: ${l.error}
`})),_}async function zt(e,a){let i={branch_name:e.branch,modules:[{module_name:e.npmName,version:e.npmVersion}]},s={[e.npmName]:a};try{t.log("Received npm publish data:",i),t.log("Target apps:",a);let r=await D(i,s);t.log("NPM upgrade result:",r);let c=nt(r,i);return k({msg_type:"text",content:{text:c}}),c}catch(r){console.error("Error processing npm publish:",r);let c=`\u274C NPM\u5305\u66F4\u65B0\u5904\u7406\u5931\u8D25
\u9519\u8BEF: ${r.message}
\u6570\u636E: ${JSON.stringify(i)}
\u76EE\u6807\u5E94\u7528: ${a.join(", ")}`;return k({msg_type:"text",content:{text:c}}),c}}var $=zt;function z(e){e.tool("npm_publish_upgrade_app","when npm package published, validate parameters and match apps. This is the first step of the npm upgrade process.",{npmName:Z.string().describe("The npmName of the published package, often found in a provided string like /\u5305\u540D\u548C\u7248\u672C\uFF1A<npmName>@<npmVersion>/"),npmVersion:Z.string().describe("The npmVersion of the published package, often found in a provided string like /\u5305\u540D\u548C\u7248\u672C\uFF1A<npmName>@<npmVersion>/"),branch:Z.string().describe("The branch of the published branch, often found in a provided string like /\u53D1\u5E03\u5206\u652F\uFF1A<branch>/")},async({npmName:a,npmVersion:i,branch:s})=>{t.log(`Starting npm upgrade process: ${a}@${i} on ${s}`);try{if(!a||!i||!s)return{content:[{type:"text",text:"\u274C \u53C2\u6570\u9A8C\u8BC1\u5931\u8D25\uFF1AnpmName\u3001npmVersion \u548C branch \u90FD\u662F\u5FC5\u9700\u7684\u53C2\u6570"}]};if(s==="master")return{content:[{type:"text",text:"\u274C \u53C2\u6570\u9A8C\u8BC1\u5931\u8D25\uFF1A\u4E0D\u5141\u8BB8\u5728 master \u5206\u652F\u4E0A\u8FDB\u884C\u66F4\u65B0\u64CD\u4F5C"}]};t.log(`\u53C2\u6570\u9A8C\u8BC1\u901A\u8FC7: ${a}@${i} on ${s}`);let r=L[a],c=`npm_upgrade_${Date.now()}`,n={npmName:a,npmVersion:i,branch:s,matchedApps:r||[],timestamp:new Date().toISOString()};if(w.set(c,n),w.set("latest_npm_upgrade",c),t.log(`\u6570\u636E\u5DF2\u4FDD\u5B58\u5230\u5185\u5B58\uFF0Ckey: ${c}`),t.log(`\u5339\u914D\u5230\u7684\u5E94\u7528: ${r?r.join(", "):"\u65E0"}`),r&&r.length>0){t.log(`\u81EA\u52A8\u6267\u884C\u5168\u90E8\u5E94\u7528\u66F4\u65B0: ${r.join(", ")}`);try{let o=await $({npmName:a,npmVersion:i,branch:s},r);return w.delete(c),w.delete("latest_npm_upgrade"),t.log("\u81EA\u52A8npm\u5305\u5347\u7EA7\u5B8C\u6210"),{content:[{type:"text",text:`\u2705 \u81EA\u52A8\u6267\u884Cnpm\u5305\u5347\u7EA7\u4EFB\u52A1\u5B8C\u6210

\u5339\u914D\u7684\u5E94\u7528: ${r.join(", ")}
\u5347\u7EA7\u5305: ${a}@${i}
\u5206\u652F: ${s}

\u8BE6\u7EC6\u7ED3\u679C:
${o}`}]}}catch(o){return t.error("\u81EA\u52A8\u6267\u884Cnpm\u5305\u5347\u7EA7\u5931\u8D25:",o),{content:[{type:"text",text:`\u274C \u81EA\u52A8\u6267\u884Cnpm\u5305\u5347\u7EA7\u5931\u8D25: ${o.message}

`}]}}}else return{content:[{type:"text",text:"\u4F60\u60F3\u8981\u66F4\u65B0\u54EA\u4E9B\u5E94\u7528\uFF0C\u8BF7\u544A\u8BC9\u6211\uFF0C\u591A\u4E2A\u5E94\u7528\u4EE5,\u5206\u9694\uFF0C\u4F8B\u5982\uFF1Aapp1,app2,app3"}]}}catch(r){return t.error("npm_publish_upgrade_app \u6267\u884C\u5931\u8D25:",r),{content:[{type:"text",text:`\u274C \u5904\u7406\u5931\u8D25: ${r.message}`}]}}}),e.tool("npm_publish_upgrade_app_running","Manually execute npm package upgrade for specified apps. Use this when: 1) No apps were auto-matched and you want to specify custom apps, 2) Auto-execution failed and you want to retry.",{apps:Z.string().describe("The apps to upgrade, can be '\u5168\u90E8' for all matched apps, or comma-separated app names like 'app1,app2,app3'")},async({apps:a})=>{t.log(`\u5F00\u59CB\u6267\u884Cnpm\u5305\u5347\u7EA7\u4EFB\u52A1\uFF0C\u76EE\u6807\u5E94\u7528: ${a}`);try{let i=w.get("latest_npm_upgrade");if(!i)return{content:[{type:"text",text:"\u274C \u672A\u627E\u5230\u5F85\u5904\u7406\u7684npm\u5347\u7EA7\u4EFB\u52A1\uFF0C\u8BF7\u5148\u6267\u884C npm_publish_upgrade_app \u5DE5\u5177\u8FDB\u884C\u53C2\u6570\u9A8C\u8BC1\u548C\u5E94\u7528\u5339\u914D"}]};let s=w.get(i);if(!s)return{content:[{type:"text",text:"\u274C \u5347\u7EA7\u4EFB\u52A1\u6570\u636E\u5DF2\u8FC7\u671F\u6216\u4E0D\u5B58\u5728\uFF0C\u8BF7\u91CD\u65B0\u6267\u884C npm_publish_upgrade_app \u5DE5\u5177"}]};let{npmName:r,npmVersion:c,branch:n,matchedApps:o}=s;t.log(`\u83B7\u53D6\u5230\u5347\u7EA7\u6570\u636E: ${r}@${c} on ${n}`),t.log(`\u5339\u914D\u7684\u5E94\u7528: ${o.join(", ")}`);let p=[];if(a.trim()==="\u5168\u90E8"){if(o.length===0)return{content:[{type:"text",text:"\u274C \u6CA1\u6709\u5339\u914D\u5230\u4EFB\u4F55\u5E94\u7528\uFF0C\u65E0\u6CD5\u6267\u884C\u5168\u90E8\u66F4\u65B0"}]};p=[...o],t.log(`\u7528\u6237\u9009\u62E9\u5168\u90E8\u66F4\u65B0\uFF0C\u76EE\u6807\u5E94\u7528: ${p.join(", ")}`)}else{if(p=a.split(",").map(_=>_.trim()).filter(_=>_),p.length===0)return{content:[{type:"text",text:"\u274C \u672A\u6307\u5B9A\u6709\u6548\u7684\u5E94\u7528\u540D\u79F0\uFF0C\u8BF7\u63D0\u4F9B\u9017\u53F7\u5206\u9694\u7684\u5E94\u7528\u5217\u8868"}]};t.log(`\u7528\u6237\u6307\u5B9A\u5E94\u7528: ${p.join(", ")}`)}t.log("\u5F00\u59CB\u6267\u884Cnpm\u5305\u5347\u7EA7...");let u=await $({npmName:r,npmVersion:c,branch:n},p);return w.delete(i),w.delete("latest_npm_upgrade"),t.log("npm\u5305\u5347\u7EA7\u5B8C\u6210"),{content:[{type:"text",text:`\u2705 npm\u5305\u5347\u7EA7\u4EFB\u52A1\u6267\u884C\u5B8C\u6210

\u76EE\u6807\u5E94\u7528: ${p.join(", ")}
\u5347\u7EA7\u5305: ${r}@${c}
\u5206\u652F: ${n}

\u8BE6\u7EC6\u7ED3\u679C:
${u}`}]}}catch(i){return t.error("npm_publish_upgrade_app_running \u6267\u884C\u5931\u8D25:",i),{content:[{type:"text",text:`\u274C npm\u5305\u5347\u7EA7\u6267\u884C\u5931\u8D25: ${i.message}

\u4F60\u53EF\u4EE5\u91CD\u65B0\u5C1D\u8BD5\u6267\u884C\u6B64\u5DE5\u5177\uFF0C\u6216\u8005\u91CD\u65B0\u5F00\u59CB\u6574\u4E2A\u6D41\u7A0B`}]}}})}import{z as N}from"zod";var P=["wsc-tee-h5","wsc"];import{execSync as Pt}from"child_process";function St(){try{return Pt("git config user.name",{encoding:"utf8"}).trim()}catch(e){return console.warn("Failed to get git user name:",e.message),"Unknown User"}}function ot(e,a){let{branch_name:i}=a,s=St();if(!e.success&&(!e.results||e.results.length===0))return`\u274C \u66F4\u65B0\u5931\u8D25
\u7528\u6237: ${s}
\u5206\u652F: ${i}
\u9519\u8BEF: ${e.error||"\u672A\u77E5\u9519\u8BEF"}`;let r=e.results?.filter(_=>_.success)||[],c=e.results?.filter(_=>!_.success)||[],n=r.length,o=c.length,u=`${e.success?"\u2705 \u5E94\u7528\u5206\u652F\u66F4\u65B0\u5B8C\u6210":"\u26A0\uFE0F \u5E94\u7528\u5206\u652F\u66F4\u65B0\u90E8\u5206\u5B8C\u6210"}
`;return u+=`\u7528\u6237: ${s}
`,u+=`\u5206\u652F: ${i}
`,u+=`\u603B\u8BA1: ${n}\u6210\u529F / ${o}\u5931\u8D25

`,r.length>0&&(u+=`\u{1F4E6} \u6210\u529F\u66F4\u65B0\u7684\u5E94\u7528:
`,r.forEach(_=>{u+=`  \u2022 ${_.appName}
`})),c.length>0&&(u+=`
\u274C \u5931\u8D25\u7684\u66F4\u65B0:
`,c.forEach(_=>{u+=`  \u2022 ${_.appName}
`,u+=`    \u9519\u8BEF: ${_.error}
`})),u}async function Nt(e){let{appNames:a,branchName:i,wscTargetBranchName:s="master"}=e;if(t.log(`\u5F00\u59CB\u521B\u5EFA\u5206\u652F ${i}\uFF0C\u57FA\u4E8E\u5206\u652F ${s}`),t.log(`\u6D89\u53CA\u5E94\u7528: ${a.join(", ")}`),!Array.isArray(a)||a.length===0)throw console.error("appNames\u5FC5\u987B\u662F\u4E00\u4E2A\u975E\u7A7A\u6570\u7EC4"),new Error("appNames\u5FC5\u987B\u662F\u4E00\u4E2A\u975E\u7A7A\u6570\u7EC4");if(typeof i!="string"||!i.trim())throw new Error("branchName\u5FC5\u987B\u662F\u4E00\u4E2A\u975E\u7A7A\u5B57\u7B26\u4E32");let r=[],c={};for(let n of a)try{if(!v(n))throw new Error(`\u5E94\u7528${n}\u4E0D\u5B58\u5728\u4E8E\u914D\u7F6E\u4E2D`);t.log(`\u5F00\u59CB\u4E3A\u5E94\u7528${n}\u521B\u5EFA\u5206\u652F${i}...`);let o=await T({appName:n,branchName:i,targetBranchName:n==="wsc"?s:"master"});if(o.message)throw new Error(`\u521B\u5EFA\u5931\u8D25: ${o.message}`);r.push({appName:n,success:!0,branch:o,message:`\u5206\u652F${i}\u521B\u5EFA\u6210\u529F`}),t.log(`\u5E94\u7528${n}\u7684\u5206\u652F${i}\u521B\u5EFA\u6210\u529F`);for(let p of P)c[p]||(c[p]=[]),c[p].push(n),t.log(`\u7F13\u5B58\u7236\u5E94\u7528${p}\u5BF9\u5B50\u5E94\u7528${n}\u7684\u66F4\u65B0\u9700\u6C42`)}catch(o){let p=o;r.push({appName:n,success:!1,error:p.message,message:`\u5206\u652F${i}\u521B\u5EFA\u5931\u8D25: ${o.message}`}),console.error(`\u5E94\u7528${n}\u7684\u5206\u652F${i}\u521B\u5EFA\u5931\u8D25:`,p.message)}for(let[n,o]of Object.entries(c))try{await et({appName:n,branchName:i,subApps:o})}catch(p){console.error(`\u66F4\u65B0\u7236\u5E94\u7528${n}\u5931\u8D25:`,p.message)}return t.log(`\u5206\u652F ${i} \u521B\u5EFA\u5B8C\u6210\uFF0C\u5171\u5904\u7406 ${a.length} \u4E2A\u5E94\u7528`),r}async function S(e){try{let{biz:a,branchName:i}=e,s=a,r=[...new Set([...s,...P])];t.log(`\u89E3\u6790\u5F97\u5230\u7684appNames: ${r.join(", ")}`);let c=await Y();t.log(`\u83B7\u53D6\u5230\u7684\u6700\u65B0hotfix\u5206\u652F\u662F: ${c}`);let n=await Nt({appNames:r,branchName:i,wscTargetBranchName:c}),o=ot({success:n.every(p=>p.success),results:n,error:n.find(p=>!p.success)?.error},{branch_name:i});return await k({msg_type:"text",content:{text:o}}),n}catch(a){throw await k({msg_type:"text",content:{text:`\u274C \u5206\u652F\u521B\u5EFA\u5931\u8D25
\u5206\u652F: ${e.branchName}
\u9519\u8BEF: ${a.message}`}}),a}}var ct={subBranchs:["ext-tee-wsc-decorate","ext-tee-wsc-decorate-h5","ext-tee-wsc-statcenter","ext-tee-assets","ext-tee-passport","ext-tee-shop","ext-tee-wsc-goods","ext-tee-wsc-ump","ext-tee-salesman","ext-tee-logger","ext-tee-wsc-im","ext-tee-wsc-trade","ext-tee-cps","ext-tee-retail-prepaid","ext-tee-guide","ext-tee-navigate","ext-tee-edu-goods","ext-tee-retail-groupbuy","ext-tee-retail-solitaire","ext-tee-wholesale","ext-tee-common","ext-tee-user","ext-tee-retail-shelf","ext-tee-live"]};function I(e){e.tool("create_ranta_h5_branch","when you need create a h5 branch for app, and have parent branch, use it",{branchName:N.string().describe("\u6211\u60F3\u8981\u521B\u5EFA\u4E00\u4E2A\u5206\u652F\uFF0C\u6BD4\u5982: \u521B\u5EFA\u5206\u652F: <branchName>\uFF0C\u5305\u542Bxx\u4E1A\u52A1"),biz:N.array(N.string().describe(`\u6211\u60F3\u8981\u521B\u5EFA\u4E00\u4E2A\u5206\u652F\uFF0C\u6BD4\u5982\uFF1A \u521B\u5EFA\u5206\u652F: hotfix/xxx\uFF0C\u5305\u542B<biz>\u4E1A\u52A1, biz \u6765\u81EA\u5206\u652F\u6570\u636E: ${ct.subBranchs.join(", ")}`))},async({branchName:a,biz:i})=>(t.log(`Creating branch ${a} on ${i}`),await S({branchName:a,biz:i}),{content:[{type:"text",text:"Successfully created branch."}]}))}function C(e){z(e),I(e)}Bt({path:Ot(process.cwd(),".env")});function Ut(e=!1){let a=new Ht({name:"front-development-tools-mcp Server",version:"2.0.1"});return C(a),t.isHTTP=e,a}async function Ft(){let e=process.env.NODE_ENV==="cli"||process.argv.includes("--stdio"),a=H(e),i=Ut(!e);if(e){let s=new Ct;await i.connect(s)}else t.log(`Initializing front-development-tools-mcp MCP Server in HTTP mode on port ${a.port}...`),await W(a.port,i)}process.argv[1]&&Ft().catch(e=>{t.error("Failed to start server:",e),process.exit(1)});export{H as a,Ut as b,Ft as c};
