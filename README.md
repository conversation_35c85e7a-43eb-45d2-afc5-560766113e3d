# Front Development Tools MCP

> 基于 Model Context Protocol (MCP) 的前端开发工具集合，提供自动化的分支管理和NPM包升级功能

## 📋 项目概述

本项目是一个专为前端开发团队设计的 MCP 服务器，通过 AI 助手可以自然语言交互的方式执行复杂的开发任务。主要功能包括：

- **🌿 智能分支管理**: 自动创建H5中台化分支，支持多应用联动
- **📦 NPM包自动升级**: 当NPM包发布后，自动更新相关应用的依赖版本
- **🔔 实时通知**: 集成飞书webhook，实时推送操作结果
- **🤖 AI友好**: 支持自然语言指令，无需记忆复杂命令

## ‼️重要声明
> 本项目为公司内部项目，包含敏感的git仓库信息，请勿外传

## 开发说明

### 1. 安装依赖

```bash
make install
```

### 2. 启动

```bash
make dev
```

### 3. 测试

```bash
make test
```

## 🚀 快速开始

### 环境要求
- Node.js >= 18.0.0
- Git 环境配置
- 相关仓库访问权限

### 配置环境变量
创建 `.env` 文件并配置以下变量：
```bash
# Git私有token (必需)
private_token=your_git_private_token

# 飞书webhook地址 (可选，用于通知)
webhook_url=https://open.feishu.cn/open-apis/bot/v2/hook/your_webhook_id

# 服务端口 (可选，默认3002)
PORT=3002
```

## 📱 MCP客户端配置

### 方式一: SSE连接 (推荐用于开发)
> 需要本地启动服务

```json
{
  "mcpServers": {
    "front-development-tools-mcp": {
      "timeout": 1000000,
      "url": "http://localhost:3002/sse"
    }
  }
}
```

### 方式二: NPX直接启动 (推荐用于生产)

```json
{
  "mcpServers": {
    "front-development-tools-mcp": {
      "timeout": 1000000,
      "command": "npx",
      "args": [
        "-y",
        "front-development-tools-mcp",
        "--stdio"
      ],
      "env": {
        "private_token": "your_git_private_token",
        "webhook_url": "your_webhook_url"
      }
    }
  }
}
```

### 方式三: StreamableHTTP连接
```json
{
  "mcpServers": {
    "front-development-tools-mcp": {
      "timeout": 1000000,
      "url": "http://localhost:3002/mcp"
    }
  }
}
```

## 🛠️ 核心功能

### 1. H5中台化分支管理 (`create_ranta_h5_branch`)

**功能描述**: 智能创建H5中台化分支，支持多应用联动和依赖关系管理

**支持的业务模块**:
```
ext-tee-wsc-decorate, ext-tee-wsc-decorate-h5, ext-tee-wsc-statcenter,
ext-tee-assets, ext-tee-passport, ext-tee-shop, ext-tee-wsc-goods,
ext-tee-wsc-ump, ext-tee-salesman, ext-tee-logger, ext-tee-wsc-im,
ext-tee-wsc-trade, ext-tee-cps, ext-tee-retail-prepaid, ext-tee-guide,
ext-tee-navigate, ext-tee-edu-goods, ext-tee-retail-groupbuy,
ext-tee-retail-solitaire, ext-tee-wholesale, ext-tee-common,
ext-tee-user, ext-tee-retail-shelf, ext-tee-live
```

**AI交互示例**:
- 🗣️ **用户**: "创建分支 hotfix/jira-9014，包含 trade"
- ⚙️ **执行过程**:
  1. 自动创建 `ext-tee-wsc-trade`、`wsc-tee-h5`、`wsc` 三个应用的分支
  2. 更新 `wsc-tee-h5` 和 `wsc` 的 `koko.repo.json` 文件中的依赖分支
  3. 推送代码并发送飞书通知

### 2. NPM包自动升级 (`npm_publish_upgrade_app`)

**功能描述**: NPM包发布后自动更新相关应用的依赖版本，支持多应用批量更新

**支持的包映射关系**:
- `@youzan/wsc-tee-trade-common` → `wsc-tee-h5`, `wsc`
- `@youzan/wsc-tee-goods-common` → `wsc-tee-h5`, `wsc`
- `@youzan/order-domain-pc-components` → `wsc-pc-trade`, `retail-node-order`, `retail-node-fulfillment`

**AI交互示例**:
- 🗣️ **用户**: "发布分支：hotfix/test-0712-2322 包名和版本：@youzan/order-domain-pc-components@1.1.9-beta.20250710151649.0"
- ⚙️ **执行过程**:
  1. 参数验证和应用匹配
  2. 在 `wsc-pc-trade`、`retail-node-order`、`retail-node-fulfillment` 中创建分支
  3. 更新 `package.json` 中的依赖版本
  4. 提交并推送代码，发送结果通知

### 3. 自定义应用升级 (`npm_publish_upgrade_app_custom`)

**功能描述**: 支持自定义应用列表的NPM包升级，提供更灵活的升级策略

**AI交互示例**:
- 🗣️ **用户**: "升级 @youzan/common-utils@2.1.0 到 wsc-tee-h5, custom-app"
- ⚙️ **执行过程**: 仅在指定的应用中进行版本升级


## 🔧 技术架构

### 核心技术栈
- **MCP SDK**: `@modelcontextprotocol/sdk` - Model Context Protocol 实现
- **TypeScript**: 类型安全的开发体验
- **Express**: HTTP服务器和SSE支持
- **Git操作**: 自动化的分支管理和代码提交
- **Webhook集成**: 飞书通知推送

### 项目结构
```
src/
├── cli.ts              # CLI入口和服务器创建
├── server.ts           # HTTP/SSE服务器实现
├── config.ts           # 配置管理
├── mcp-tools/          # MCP工具注册
│   ├── create-branch.ts    # 分支创建工具
│   ├── npm-publish.ts      # NPM升级工具
│   └── index.ts           # 工具注册入口
├── packages/           # 核心业务逻辑
│   ├── create-branch/     # 分支管理功能
│   └── npm-publish/       # NPM升级功能
└── utils/              # 工具函数
    ├── logger.ts          # 日志系统
    ├── webhook.ts         # 通知推送
    └── memory-store.ts    # 内存存储
```

## 🚀 部署运行

### 后台持久运行 (PM2)

1. **安装PM2**
```bash
npm install -g pm2
```

2. **启动服务**
```bash
pm2 start make --name "front-development-tools-mcp" -- dev
```

3. **查看运行状态**
```bash
pm2 list
pm2 logs front-development-tools-mcp
```

4. **进程管理**
```bash
# 重启服务
pm2 restart front-development-tools-mcp

# 停止服务
pm2 stop front-development-tools-mcp

# 删除进程
pm2 delete front-development-tools-mcp
```

5. **开机自启**
```bash
pm2 save
pm2 startup
# 执行输出的命令
```

### Docker部署 (可选)
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build
EXPOSE 3002
CMD ["npm", "start"]
```

## 🤖 AI助手使用示例

### 创建分支示例
```
用户: "帮我创建一个分支 hotfix/fix-trade-bug，包含 trade 和 goods 业务"
AI: 正在为您创建分支...
✅ 成功创建以下分支：
- ext-tee-wsc-trade: hotfix/fix-trade-bug
- ext-tee-wsc-goods: hotfix/fix-trade-bug
- wsc-tee-h5: hotfix/fix-trade-bug
- wsc: hotfix/fix-trade-bug
并已更新相关依赖配置文件
```

### NPM升级示例
```
用户: "发布分支：feature/new-feature 包名和版本：@youzan/order-domain-pc-components@2.1.0"
AI: 正在升级NPM包版本...
✅ 成功在以下应用中升级到 2.1.0：
- wsc-pc-trade
- retail-node-order
- retail-node-fulfillment
所有更改已提交并推送到 feature/new-feature 分支
```

## 📊 监控和日志

### 日志系统
项目内置了完整的日志系统，支持不同运行模式：
- **开发模式**: 控制台输出详细日志
- **HTTP模式**: 结构化日志输出
- **生产模式**: 优化的日志格式

### 通知系统
集成飞书webhook，实时推送操作结果：
- ✅ 操作成功通知
- ❌ 错误信息推送
- 📊 详细的执行报告

## 🔒 安全考虑

- **Token管理**: Git私有token通过环境变量安全传递
- **权限控制**: 仅支持非master分支操作
- **参数验证**: 严格的输入参数校验
- **错误处理**: 完善的异常捕获和处理机制

## 🤝 贡献指南

### 开发流程
1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

### 代码规范
- 使用 TypeScript 进行类型安全开发
- 遵循 ESLint 代码规范
- 编写单元测试覆盖核心功能
- 添加详细的代码注释

## 📝 更新日志

### v1.0.10 (当前版本)
- ✨ 支持H5中台化分支自动创建
- ✨ NPM包自动升级功能
- ✨ 飞书webhook通知集成
- ✨ 多种MCP连接方式支持
- 🐛 修复分支创建时的依赖更新问题

## 📞 支持与反馈

如果您在使用过程中遇到问题或有改进建议，请：
1. 查看项目文档和FAQ
2. 提交 Issue 描述问题
3. 联系开发团队获取支持

---

**⚠️ 注意**: 本项目包含公司内部敏感信息，请确保在安全的环境中使用，不要将配置信息泄露到公共环境。

