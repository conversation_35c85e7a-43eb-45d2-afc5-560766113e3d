# 说明
> 一些前端常用的工具集合，通过mcp协议进行交互

## ‼️重要
> 本项目为公司内部项目，内有一些git仓库信息，请勿外传

## 开发说明

### 1. 安装依赖

```bash
make install
```

### 2. 启动

```bash
make dev
```

### 3. 测试

```bash
make test
```

## 使用说明

### sse连接
> 需要本地启动服务
```json
{
  "mcpServers": {
    "front-development-tools-mcp": {
      "timeout": 1000000,
      "url": "http://localhost:3002/sse"
    },
  }
}
```

### npx启动

```json
{
  "mcpServers": {
    "front-development-tools-mcp": {
      "timeout": 1000000,
      "command": "npx",
      "args": [
        "-y",
        "front-development-tools-mcp",
        "--stdio"
      ],
      "env": {
        "private_token": "xxx",
        "webhook_url": "xxx"
      }
    },
  }
}

```

## 工具列表

| 工具名称 | 描述 | AI交互示例 |
|---------|------|------|
| create_ranta_h5_branch | 创建H5中台化分支 | <ul><li>输入: 创建分支hotfix/jira-9014,包含trade</li><li>执行预期: 程序会依次创建ext-tee-wsc-trade、wsc-tee-h5、wsc三个应用的hotfix/jira-9014分支,并且会将wsc-tee-h5和wsc的koko.repo.json文件中的ext-tee-wsc-trade分支更新为hotfix/jira-9014</li></ul> |
| npm_publish_upgrade_app | 当npm包发布后更新应用npm版本 | <ul><li>输入： 发布分支：hotfix/test-0712-2322 包名和版本：@youzan/order-domain-pc-components@ 1.1.9-beta.20250710151649.0</li><li>执行预期：程序会将wsc-pc-trade、retail-node-order、retail-node-fulfillment三个应用的package.json文件中的@youzan/order-domain-pc-components版本更新为1.1.9-beta.20250710151649.0，并创建hotfix/test-0712-2322分支，提交并推送代码</li></ul> |


## 后台持久运行

### 安装pm2

```
npm i -g pm2
```

### 启动

```
pm2 start make --name "front-development-tools-mcp" -- dev
```

### 查看

```
pm2 list
```

### 保存进程

```
pm2 save
```

### 开机自启

```
pm2 startup
```
执行输出的命令

### 重启进程

```
pm2 restart front-development-tools-mcp
```

### 查看日志

```
pm2 logs front-development-tools-mcp
```

