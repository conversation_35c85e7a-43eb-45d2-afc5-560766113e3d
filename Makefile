
# 安装npm包yarn
yyarn = yarn --registry=https://registry.npmmirror.com/ --disturl=https://npm.taobao.org/dist
# 运行yarn
binyarn = yarn

install: yarn-install 

dev: yarn-dev

test: yarn-test

yarn-install:
	$(yyarn) install

yarn-dev:
	$(binyarn) dev

yarn-test: 
	$(binyarn) dev:test
publish:
	@echo '--------------- Start publishing ------------------'
	@yarn add @youzan/npm-platform-publish -DW
	@git checkout .
	@npx npp publish --taskId $(shell echo $$HARDWORKER_TASK_ID)
	@echo '--------------- Publish successfully --------------'
