/**
 * 依次执行异步方法并打印结果
 * @param {{[funcName: string]: () => Promise<unknown>}} taskMap 异步方法集合
 * @returns {Promise<void>}
 */
export async function test(taskMap) {
  console.log('Starting test execution...');
  console.log(`Total tasks: ${Object.keys(taskMap).length}`);
  
  for (const [name, task] of Object.entries(taskMap)) {
    const startTime = Date.now();
    console.log(`[${new Date().toISOString()}] Starting task: ${name}`);
    
    try {
      const result = await (task as any)();
      const duration = Date.now() - startTime;
      console.log(`[${new Date().toISOString()}] Task ${name} completed in ${duration}ms`);
      console.log(`✅ ${name} result:`, result);
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`[${new Date().toISOString()}] Task ${name} failed after ${duration}ms`);
      console.error(`${name} error:`, error);
    }
  }
  
  console.log('All tasks completed');
}