import { getWscLastHotfixBranch, getProjectRepo, getFileContent, updateAppKokoRepo, updatePackageJson } from "../src/gitlib/tools.js";
import { getProjectInfo, createBranch } from "../src/gitlib/api.js";

export const getWscLastHotfixBranchTest = () => {
  return getWscLastHotfixBranch();
};

export const getProjectRepoTest = () => {
  return getProjectRepo("wsc-pc-trade");
};

export const getProjectInfoTest = () => {
  return getProjectInfo({ appName: "wsc-tee-h5" });
};

export const getRepositoryFileContentTest = () => {
  return getFileContent({
    appName: "wsc-tee-h5",
    filePath: "koko.repo.json",
    branch: "master",
  });
};


/**
 * 生成测试分支并更新koko.repo.json文件内容
 */
export const updateAppKokoRepoTest = () => {
  const branchName = "hotfix/test-" + Date.now();
  return createBranch({
    appName: "wsc-tee-h5",
    branchName,
    targetBranchName: "master",
  }).then(() => {
    return updateAppKokoRepo({
      appName: "wsc-tee-h5",
      branchName,
      subApps: ["ext-tee-wsc-ump", "ext-tee-wsc-trade"],
    });
  });
};

/**
 * 生成测试分支并更新package.json文件内容
 */
export const updatePackageJsonTest = () => {
  const branchName = "hotfix/test-" + Date.now();
  return createBranch({
    appName: "wsc-pc-trade",
    branchName,
  }).then(() => {
    return updatePackageJson({
      appName: "wsc-pc-trade",
      branchName,
      path: 'client',
      packageInfo: {
        name: "@youzan/order-domain-pc-components",
        version: "1.1.14-beta.20250911141634.0",
      },
    });
  });
};
