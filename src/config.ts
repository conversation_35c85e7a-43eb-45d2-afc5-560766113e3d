import { config as loadEnv } from "dotenv";
import yargs from "yargs";
import { hideBin } from "yargs/helpers";
import { resolve } from "path";

interface ServerConfig {
  port: number;
}

export function getServerConfig(isStdioMode: boolean): ServerConfig {
  // Parse command line arguments
  const argv = yargs(hideBin(process.argv))
    .options({
      env: {
        type: "string",
        description: "Path to custom .env file to load environment variables from",
      },
      port: {
        type: "number",
        description: "Port to run the server on",
      },
    })
    .help()
    .parseSync();

  // Load environment variables ASAP from custom path or default
  let envFilePath: string;

  if (argv["env"]) {
    envFilePath = resolve(argv["env"]);
  } else {
    envFilePath = resolve(process.cwd(), ".env");
  }

  // Override anything auto-loaded from .env if a custom file is provided.
  loadEnv({ path: envFilePath, override: true });

  const config = {
    port: 3002,
  };

  // Handle PORT
  if (argv.port) {
    config.port = argv.port;
  } else if (process.env.PORT) {
    config.port = parseInt(process.env.PORT, 10);
  }

  return {
    ...config,
  };
}
