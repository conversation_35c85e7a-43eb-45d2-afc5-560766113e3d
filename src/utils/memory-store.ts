/**
 * 简单的内存存储模块
 * 用于在工具之间共享数据
 */

interface MemoryStore {
  [key: string]: any;
}

class MemoryStoreManager {
  private store: MemoryStore = {};

  /**
   * 设置数据
   * @param key 键名
   * @param value 值
   */
  set(key: string, value: any): void {
    this.store[key] = value;
  }

  /**
   * 获取数据
   * @param key 键名
   * @returns 值或undefined
   */
  get(key: string): any {
    return this.store[key];
  }

  /**
   * 删除数据
   * @param key 键名
   * @returns 是否删除成功
   */
  delete(key: string): boolean {
    if (key in this.store) {
      delete this.store[key];
      return true;
    }
    return false;
  }

  /**
   * 检查键是否存在
   * @param key 键名
   * @returns 是否存在
   */
  has(key: string): boolean {
    return key in this.store;
  }

  /**
   * 清空所有数据
   */
  clear(): void {
    this.store = {};
  }

  /**
   * 获取所有键
   * @returns 键名数组
   */
  keys(): string[] {
    return Object.keys(this.store);
  }

  /**
   * 获取存储的数据数量
   * @returns 数据数量
   */
  size(): number {
    return Object.keys(this.store).length;
  }
}

// 创建全局单例实例
const memoryStore = new MemoryStoreManager();

export default memoryStore;
export { MemoryStoreManager };
