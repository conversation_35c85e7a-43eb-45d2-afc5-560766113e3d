import https from "https";
import http from "http";
import { URL } from "url";

// 原生 Node.js fetch 实现
function fetch(url: string, options: any = {}): Promise<any> {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const { method = "GET", headers = {}, body } = options;

    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (urlObj.protocol === "https:" ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: method.toUpperCase(),
      headers: {
        "User-Agent": "Node.js",
        ...headers,
      },
    };

    // 如果有 body，设置 Content-Length
    if (body) {
      const bodyBuffer = Buffer.from(body, "utf8");
      requestOptions.headers["Content-Length"] = bodyBuffer.length;
    }

    const protocol = urlObj.protocol === "https:" ? https : http;

    const req = protocol.request(requestOptions, (res: any) => {
      let data = "";

      res.on("data", (chunk: any) => {
        data += chunk;
      });

      res.on("end", () => {
        const response = {
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          statusText: res.statusMessage,
          headers: res.headers,
          text: () => Promise.resolve(data),
          json: () => {
            try {
              return Promise.resolve(JSON.parse(data));
            } catch (e) {
              return Promise.reject(new Error("Invalid JSON response"));
            }
          },
        };
        resolve(response);
      });
    });

    req.on("error", (error) => {
      reject(error);
    });

    // 发送请求体
    if (body) {
      req.write(body);
    }

    req.end();
  });
}

const webhookUrl = "https://open.feishu.cn/open-apis/bot/v2/hook/d25a223f-be15-42e2-b636-6af907680caf";

export function notify(payload: any): Promise<any> {
  return fetch(process.env.webhook_url || webhookUrl, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(payload),
  });
}
