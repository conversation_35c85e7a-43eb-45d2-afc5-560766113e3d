/**
 * 创建H5中台化分支
 */
import { z } from "zod";
import { Logger } from "~/utils/logger.js";

import branchData from '../../library/branch.json';

/**
 * 
 * 把trade hotfix/20250715分支上中台化大巴车
 * 把wsc-h5-trade hotfix/20250715分支上node端大巴车
 * 
 */

export default function registerAutoGetToBusTool(server: any) {
  server.tool(
    "get_to_node_bus",
    "when you need some branch Get on the node or service bus, use it",
    {
      branchName: z
        .string()
        .describe(
          "你想要上车的大巴车分支名称，比如：hotfix/20250715"
        ),
      appName: z.array(
        z
          .string()
          .describe(
            `你想要上车的大巴车应用名称，比如：wsc-h5-trade`
          )
      ),
    },
    async ({ branchName, appName }: any) => {
      Logger.log(`你想要上车的大巴车分支名称：${branchData.subBranchs.join(', ')}`);
      Logger.log(`你想要上车的大巴车应用名称：${appName}`);
      return {
        content: [
          {
            type: "text",
            text: "This function has not been realized for the time being.",
          },
        ],
      };
    }
  );

   server.tool(
    "get_to_ranta_bus",
    "when you need some branch Get on the H5 or 中台化 bus, use it",
    {
      branchName: z
        .string()
        .describe(
          "i want to get to the bus with branch name like<branchName>, includes trade and goods biz"
        ),
      biz: z.array(
        z
          .string()
          .describe(
            `i want to get to the bus with biz name like hotfix/xxx, includes <biz> like in "${branchData}"`
          )
      ),
    },
    async ({ branchName, biz }: any) => {
      Logger.log(`你想要上车的大巴车分支名称：${branchName}`);
      Logger.log(`你想要上车的大巴车业务线名称：${biz}`);
      return {
        content: [
          {
            type: "text",
            text: "This function has not been realized for the time being",
          },
        ],
      };
    }
  );
}
