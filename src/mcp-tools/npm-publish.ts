
/**
 * npm包自动更新应用并提交 - 重构版本
 * 第一个工具：参数验证和应用匹配阶段
 */
import { z } from "zod";
import { Logger } from "~/utils/logger.js";
import memoryStore from "~/utils/memory-store.js";
import { packageAppMaps } from "../packages/npm-publish/constants.js";
import mainWithCustomApps from "../packages/npm-publish/main-custom.js";

export default function registerNpmpublishTool(server: any) {
  // 第一个工具：npm_publish_upgrade_app - 参数验证和应用匹配
  server.tool(
    "npm_publish_upgrade_app",
    "when npm package published, validate parameters and match apps. This is the first step of the npm upgrade process.",
    {
      npmName: z
        .string()
        .describe(
          "The npmName of the published package, often found in a provided string like /包名和版本：<npmName>@<npmVersion>/"
        ),
      npmVersion: z
        .string()
        .describe(
          "The npmVersion of the published package, often found in a provided string like /包名和版本：<npmName>@<npmVersion>/"
        ),
      branch: z
        .string()
        .describe(
          "The branch of the published branch, often found in a provided string like /发布分支：<branch>/"
        ),
    },
    async ({ npmName, npmVersion, branch }: any) => {
      Logger.log(
        `Starting npm upgrade process: ${npmName}@${npmVersion} on ${branch}`
      );

      try {
        // 1. 参数验证
        if (!npmName || !npmVersion || !branch) {
          return {
            content: [
              {
                type: "text",
                text: "❌ 参数验证失败：npmName、npmVersion 和 branch 都是必需的参数",
              },
            ],
          };
        }

        // 验证分支不能是master
        if (branch === "master") {
          return {
            content: [
              {
                type: "text",
                text: "❌ 参数验证失败：不允许在 master 分支上进行更新操作",
              },
            ],
          };
        }

        Logger.log(`参数验证通过: ${npmName}@${npmVersion} on ${branch}`);

        // 2. 根据npm包信息匹配对应的应用列表
        const matchedApps = packageAppMaps[npmName as keyof typeof packageAppMaps];

        // 3. 将验证通过的参数和匹配结果保存到内存中
        const storeKey = `npm_upgrade_${Date.now()}`;
        const upgradeData = {
          npmName,
          npmVersion,
          branch,
          matchedApps: matchedApps || [],
          timestamp: new Date().toISOString(),
        };

        memoryStore.set(storeKey, upgradeData);
        memoryStore.set('latest_npm_upgrade', storeKey); // 保存最新的升级任务key

        Logger.log(`数据已保存到内存，key: ${storeKey}`);
        Logger.log(`匹配到的应用: ${matchedApps ? matchedApps.join(', ') : '无'}`);

        // 4. 根据匹配结果执行不同的逻辑
        if (matchedApps && matchedApps.length > 0) {
          // 匹配到应用列表，自动执行全部更新
          Logger.log(`自动执行全部应用更新: ${matchedApps.join(', ')}`);

          try {
            // 直接执行升级操作
            const result = await mainWithCustomApps({
              npmName,
              npmVersion,
              branch,
            }, matchedApps);

            // 清理内存中的升级任务数据
            memoryStore.delete(storeKey);
            memoryStore.delete('latest_npm_upgrade');

            Logger.log(`自动npm包升级完成`);

            return {
              content: [
                {
                  type: "text",
                  text: `✅ 自动执行npm包升级任务完成\n\n匹配的应用: ${matchedApps.join(', ')}\n升级包: ${npmName}@${npmVersion}\n分支: ${branch}\n\n详细结果:\n${result}`,
                },
              ],
            };
          } catch (error: any) {
            Logger.error("自动执行npm包升级失败:", error);

            // 发生错误时不清理数据，允许用户手动重试
            return {
              content: [
                {
                  type: "text",
                  text: `❌ 自动执行npm包升级失败: ${error.message}\n\n`,
                },
              ],
            };
          }
        } else {
          return {
            content: [
              {
                type: "text",
                text: `你想要更新哪些应用，请告诉我，多个应用以,分隔，例如：app1,app2,app3`,
              },
            ],
          };
        }
      } catch (error: any) {
        Logger.error("npm_publish_upgrade_app 执行失败:", error);
        return {
          content: [
            {
              type: "text",
              text: `❌ 处理失败: ${error.message}`,
            },
          ],
        };
      }
    }
  );

  // 第二个工具：npm_publish_upgrade_app_running - 手动执行应用更新任务
  server.tool(
    "npm_publish_upgrade_app_running",
    "Manually execute npm package upgrade for specified apps. Use this when: 1) No apps were auto-matched and you want to specify custom apps, 2) Auto-execution failed and you want to retry.",
    {
      apps: z
        .string()
        .describe(
          "The apps to upgrade, can be '全部' for all matched apps, or comma-separated app names like 'app1,app2,app3'"
        ),
    },
    async ({ apps }: any) => {
      Logger.log(`开始执行npm包升级任务，目标应用: ${apps}`);

      try {
        // 1. 获取最新的升级任务数据
        const latestUpgradeKey = memoryStore.get('latest_npm_upgrade');
        if (!latestUpgradeKey) {
          return {
            content: [
              {
                type: "text",
                text: "❌ 未找到待处理的npm升级任务，请先执行 npm_publish_upgrade_app 工具进行参数验证和应用匹配",
              },
            ],
          };
        }

        const upgradeData = memoryStore.get(latestUpgradeKey);
        if (!upgradeData) {
          return {
            content: [
              {
                type: "text",
                text: "❌ 升级任务数据已过期或不存在，请重新执行 npm_publish_upgrade_app 工具",
              },
            ],
          };
        }

        const { npmName, npmVersion, branch, matchedApps } = upgradeData;
        Logger.log(`获取到升级数据: ${npmName}@${npmVersion} on ${branch}`);
        Logger.log(`匹配的应用: ${matchedApps.join(', ')}`);

        // 2. 处理用户指定的应用列表
        let targetApps: string[] = [];

        if (apps.trim() === "全部") {
          // 用户选择全部更新
          if (matchedApps.length === 0) {
            return {
              content: [
                {
                  type: "text",
                  text: "❌ 没有匹配到任何应用，无法执行全部更新",
                },
              ],
            };
          }
          targetApps = [...matchedApps];
          Logger.log(`用户选择全部更新，目标应用: ${targetApps.join(', ')}`);
        } else {
          // 用户指定特定应用
          targetApps = apps.split(',').map((app: string) => app.trim()).filter((app: string) => app);
          if (targetApps.length === 0) {
            return {
              content: [
                {
                  type: "text",
                  text: "❌ 未指定有效的应用名称，请提供逗号分隔的应用列表",
                },
              ],
            };
          }
          Logger.log(`用户指定应用: ${targetApps.join(', ')}`);
        }

        // 3. 执行实际的npm包版本更新操作
        Logger.log(`开始执行npm包升级...`);

        // 使用自定义应用列表执行升级
        const result = await mainWithCustomApps({
          npmName,
          npmVersion,
          branch,
        }, targetApps);

        // 4. 清理内存中的升级任务数据
        memoryStore.delete(latestUpgradeKey);
        memoryStore.delete('latest_npm_upgrade');

        Logger.log(`npm包升级完成`);

        return {
          content: [
            {
              type: "text",
              text: `✅ npm包升级任务执行完成\n\n目标应用: ${targetApps.join(', ')}\n升级包: ${npmName}@${npmVersion}\n分支: ${branch}\n\n详细结果:\n${result}`,
            },
          ],
        };

      } catch (error: any) {
        Logger.error("npm_publish_upgrade_app_running 执行失败:", error);

        // 发生错误时不清理数据，允许用户重试
        return {
          content: [
            {
              type: "text",
              text: `❌ npm包升级执行失败: ${error.message}\n\n你可以重新尝试执行此工具，或者重新开始整个流程`,
            },
          ],
        };
      }
    }
  );
}
