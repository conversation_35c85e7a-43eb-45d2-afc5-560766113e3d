/**
 * 创建H5中台化分支
 */
import { z } from "zod";
import { Logger } from "~/utils/logger.js";
import { __dirname } from "~/utils/patch.js";

import createBranch from "../packages/create-branch/main.js";
import branchData from '../../library/branch.json';

export default function registerCreateBranchTool(server: any) {
  server.tool(
    "create_ranta_h5_branch",
    "when you need create a h5 branch for app, and have parent branch, use it",
    {
      branchName: z
        .string()
        .describe(
          "我想要创建一个分支，比如: 创建分支: <branchName>，包含xx业务"
        ),
      biz: z.array(
        z
          .string()
          .describe(
            `我想要创建一个分支，比如： 创建分支: hotfix/xxx，包含<biz>业务, biz 来自分支数据: ${branchData.subBranchs.join(', ')}`
          )
      ),
    },
    async ({ branchName, biz }: any) => {
      Logger.log(`Creating branch ${branchName} on ${biz}`);
      await createBranch({ branchName, biz });
      return {
        content: [
          {
            type: "text",
            text: "Successfully created branch.",
          },
        ],
      };
    }
  );
}
