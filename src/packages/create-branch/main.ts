import { PARENT_APPS } from "./constants.js";
import { Logger } from "~/utils/logger.js";
import { notify } from "../../utils/webhook.js";
import { generateNotificationText } from "./notification.js";
import { createBranch as createGitBranch } from "~/gitlib/api.js";
import { getWscLastHotfixBranch } from "~/gitlib/tools.js";
import { getProjectId, updateAppKokoRepo } from "~/gitlib/tools.js";

/**
 * @typedef {Object} CreateBranchResult
 * @property {string} appName
 * @property {boolean} success
 * @property {string} [error]
 * @property {string} message
 */

/**
 * @typedef {Object} CreateBranchOptions
 * @property {string[]} biz
 * @property {string} branchName
 */

/**
 * 批量创建Git分支
 * @param {Object} options - 参数对象
 * @param {string[]} options.appNames - 应用名称列表
 * @param {string} options.branchName - 要创建的分支名称
 * @param {string} [options.wscTargetBranchName='master'] - 基于哪个分支创建新分支，默认为master
 * @returns {Promise<Object[]>} 返回包含每个应用分支创建结果的Promise
 */
/**
 * @param {Object} options
 * @param {string[]} options.appNames
 * @param {string} options.branchName
 * @param {string} [options.wscTargetBranchName='master']
 * @returns {Promise<CreateBranchResult[]>}
 */
async function _createBranch(options: any): Promise<any[]> {
  const { appNames, branchName, wscTargetBranchName = "master" } = options;

  Logger.log(`开始创建分支 ${branchName}，基于分支 ${wscTargetBranchName}`);
  Logger.log(`涉及应用: ${appNames.join(", ")}`);

  // 参数验证
  if (!Array.isArray(appNames) || appNames.length === 0) {
    console.error("appNames必须是一个非空数组");
    throw new Error("appNames必须是一个非空数组");
  }
  if (typeof branchName !== "string" || !branchName.trim()) {
    throw new Error("branchName必须是一个非空字符串");
  }

  // 存储所有创建结果
  const results: Array<{
    appName: string;
    success: boolean;
    error?: string;
    message: string;
    branch?: any;
  }> = [];

  // 存储需要更新的父应用信息 {parentApp: [subApps]}
  const parentUpdates: Record<string, string[]> = {};

  // 依次为每个应用创建分支
  for (const appName of appNames) {
    // appName is string type
    try {
      // 检查应用是否存在于配置中
      if (!getProjectId(appName)) {
        throw new Error(`应用${appName}不存在于配置中`);
      }

      Logger.log(`开始为应用${appName}创建分支${branchName}...`);
      /** @type {{ message?: string }} */
      const response = await createGitBranch({
        appName,
        branchName,
        targetBranchName: appName === "wsc" ? wscTargetBranchName : "master",
      });
      // const response = {};

      // 检查API响应是否成功
      if (response.message) {
        throw new Error(`创建失败: ${response.message}`);
      }

      results.push({
        appName,
        success: true,
        branch: response,
        message: `分支${branchName}创建成功`,
      });
      Logger.log(`应用${appName}的分支${branchName}创建成功`);

      // 记录父应用需要更新
      // 固定父应用为wsc和wsc-tee-h5
      for (const parentApp of PARENT_APPS) {
        if (!parentUpdates[parentApp]) {
          parentUpdates[parentApp] = [];
        }
        parentUpdates[parentApp].push(appName);
        Logger.log(`缓存父应用${parentApp}对子应用${appName}的更新需求`);
      }
    } catch (error: any) {
      /** @type {Error} */ const err = error;
      results.push({
        appName,
        success: false,
        error: err.message,
        message: `分支${branchName}创建失败: ${error.message}`,
      });
      console.error(`应用${appName}的分支${branchName}创建失败:`, err.message);
    }
  }

  // 批量更新父应用
  for (const [parentApp, subApps] of Object.entries(parentUpdates)) {
    try {
      await updateAppKokoRepo({
        appName: parentApp,
        branchName: branchName,
        subApps: subApps as string[],
      });
    } catch (error: any) {
      console.error(`更新父应用${parentApp}失败:`, error.message);
    }
  }

  Logger.log(`分支 ${branchName} 创建完成，共处理 ${appNames.length} 个应用`);
  return results;
}

export default async function main(options: any): Promise<any> {
  try {
    const { biz, branchName } = options;
    const appNames = biz;

    // 添加固定父应用wsc和wsc-tee-h5
    const allAppNames = [...new Set([...appNames, ...PARENT_APPS])];

    Logger.log(`解析得到的appNames: ${allAppNames.join(", ")}`);

    const branch = await getWscLastHotfixBranch();
    Logger.log(`获取到的最新hotfix分支是: ${branch}`);
    const results = await _createBranch({
      appNames: allAppNames,
      branchName,
      wscTargetBranchName: branch,
    });

    // 生成通知文本
    const notificationText = generateNotificationText(
      {
        success: results.every((r) => r.success),
        results,
        error: results.find((r) => !r.success)?.error,
      },
      {
        branch_name: branchName,
      }
    );

    // 发送通知
    await notify({
      msg_type: "text",
      content: {
        text: notificationText,
      },
    });

    return results;
  } catch (error: any) {
    // 发送错误通知
    await notify({
      msg_type: "text",
      content: {
        text: `❌ 分支创建失败\n分支: ${options.branchName}\n错误: ${error.message}`,
      },
    });
    throw error;
  }
}
