import { execSync } from 'child_process';

// 获取git配置的用户名
function getGitUserName(): string {
  try {
    return execSync('git config user.name', { encoding: 'utf8' }).trim();
  } catch (/** @type {Error} */ error: any) {
    console.warn('Failed to get git user name:', error.message);
    return 'Unknown User';
  }
}

/**
 * @typedef {Object} NotificationOperationResult
 * @property {boolean} success - 操作是否成功
 * @property {string} appName - 应用名称
 * @property {string} [error] - 错误信息（可选）
 */

/**
 * @typedef {Object} NotificationResult
 * @property {boolean} success - 整体操作是否成功
 * @property {string} [error] - 错误信息（可选）
 * @property {NotificationOperationResult[]} [results] - 操作结果列表（可选）
 */

/**
 * @typedef {Object} OriginalData
 * @property {string} branch_name - 分支名称
 */

/**
 * 生成通知文本
 * @param {NotificationResult} result - 通知结果对象
 * @param {OriginalData} originalData - 原始数据对象
 * @returns {string} 格式化的通知文本
 */
function generateNotificationText(result: any, originalData: any): string {
  const { branch_name } = originalData;
  const user_name = getGitUserName();

  // 如果没有 results 数组，说明是早期失败（用户验证、分支验证等）
  if (!result.success && (!result.results || result.results.length === 0)) {
    return `❌ 更新失败\n用户: ${user_name}\n分支: ${branch_name}\n错误: ${result.error || '未知错误'}`;
  }

  // 计算成功和失败数量
  const successResults = result.results?.filter(/** @param {NotificationOperationResult} r */ (r: any) => r.success) || [];
const failedResults = result.results?.filter(/** @param {NotificationOperationResult} r */ (r: any) => !r.success) || [];
  const successfulOperations = successResults.length;
  const failedOperations = failedResults.length;

  // 根据整体结果决定标题
  const title = result.success ? '✅ 应用分支更新完成' : '⚠️ 应用分支更新部分完成';

  let text = `${title}\n`;
  text += `用户: ${user_name}\n`;
  text += `分支: ${branch_name}\n`;
  text += `总计: ${successfulOperations}成功 / ${failedOperations}失败\n\n`;

  if (successResults.length > 0) {
    text += `📦 成功更新的应用:\n`;
    successResults.forEach(/** @param {NotificationOperationResult} result */ (result: any) => {
      text += `  • ${result.appName}\n`;
    });
  }

  if (failedResults.length > 0) {
    text += `\n❌ 失败的更新:\n`;
    failedResults.forEach(/** @param {NotificationOperationResult} result */ (result: any) => {
      text += `  • ${result.appName}\n`;
      text += `    错误: ${result.error}\n`;
    });
  }

  return text;
}

export { generateNotificationText, getGitUserName };

export default {
  generateNotificationText,
  getGitUserName
};
