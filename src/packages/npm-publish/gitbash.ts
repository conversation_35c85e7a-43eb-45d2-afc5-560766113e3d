import { execSync } from "child_process";
import fs from "fs";
import path from "path";
import { clientDirMaps } from "./constants.js";
import { __dirname } from "~/utils/patch.js";
import { getProjectRepo, updatePackage<PERSON>son } from "~/gitlib/tools.js";
import { getBranch, createBranch } from "~/gitlib/api.js";
import { Logger } from "~/utils/logger.js";


// Helper function to get current branch name
function getCurrentBranch(): string {
  try {
    return execSync("git branch --show-current", { encoding: "utf8" }).trim();
  } catch (error) {
    return "unknown";
  }
}

// Helper function to check if working directory is clean
function isWorkingDirectoryClean(): boolean {
  try {
    const status = execSync("git status --porcelain", { encoding: "utf8" });
    return !status.trim();
  } catch (error) {
    return false;
  }
}

// Helper function to get or clone project directory
async function getOrCloneProjectDir(
  appName: string,
  baseGithubDir = path.join(__dirname, "../github")
): Promise<string> {
  // 构建项目目录路径
  const projectDir = path.join(baseGithubDir, appName);

  // 检查项目目录是否存在
  if (fs.existsSync(projectDir)) {
    Logger.log(`✓ Project directory already exists: ${projectDir}`);
    return projectDir;
  }

  // 从 gitlab API 获取 git 地址
  let gitUrl;
  try {
    gitUrl = await getProjectRepo(appName);
    if (!gitUrl) {
      throw new Error(`Failed to get repository URL for app '${appName}'`);
    }
  } catch (error: any) {
    throw new Error(`Failed to get git repository URL for app '${appName}': ${error.message}`);
  }

  Logger.log(`Project directory not found, cloning from: ${gitUrl}`);

  // 确保基础目录存在
  if (!fs.existsSync(baseGithubDir)) {
    fs.mkdirSync(baseGithubDir, { recursive: true });
    Logger.log(`Created base directory: ${baseGithubDir}`);
  }

  // 克隆项目
  try {
    execSync(`git clone ${gitUrl} ${projectDir}`, { stdio: "inherit" });
    Logger.log(`✅ Successfully cloned project to: ${projectDir}`);

    return projectDir;
  } catch (error: any) {
    throw new Error(`Failed to clone project: ${error.message}`);
  }
}

/**
 * 创建或切换到指定分支，并可选择更新npm包
 * 优化版本：使用GitLab API进行远程分支操作，减少本地git命令依赖
 *
 * 优化流程：
 * 1. 判断本地代码是否存在，如果不存在git clone仓库
 * 2. 使用getBranch API判断远端是否已经存在该分支，如果不存在使用createBranch API创建
 * 3. 使用updatePackageJson API修改package.json中npm包版本
 * 4. 执行git fetch，切换到目标分支，执行yarn然后push
 *
 * @param {Object} options - 配置选项
 * @param {string} options.appName - 应用名称（将根据此名称从getProjectRepo获取git地址并管理项目目录）
 * @param {string} [options.baseGithubDir] - github项目基础目录（默认为当前项目的github文件夹）
 * @param {string} options.branchName - 目标分支名称
 * @param {string} [options.baseBranch='master'] - 基础分支（当目标分支不存在时从此分支创建）
 * @param {boolean} [options.cleanWorkingDir=true] - 是否清理工作目录
 * @param {boolean} [options.updatePackages=false] - 是否更新npm包
 * @param {string} [options.packageName] - 要更新的npm包名称
 * @param {string} [options.packageVersion] - 要更新的npm包版本
 * @param {boolean} [options.commitAndPush=true] - 是否提交并推送更改
 * @param {string} [options.commitMessage] - 自定义提交信息
 * @returns {Promise<Object>} 返回操作结果
 */
async function createOrSwitchBranch(options: any): Promise<any> {
  const {
    appName,
    baseGithubDir = path.join(__dirname, "../github"),
    branchName,
    baseBranch = "master",
    cleanWorkingDir = true,
    updatePackages = false,
    packageName,
    packageVersion,
    commitAndPush = true,
    commitMessage,
  } = options;

  // 验证必需参数
  if (!appName || !branchName) {
    throw new Error("appName and branchName are required");
  }

  // 根据应用名称获取对应的客户端目录
  const clientDir = clientDirMaps[appName] || '';
  if (clientDir === undefined) {
    throw new Error(
      `Client directory mapping not found for app '${appName}'. Available apps: ${Object.keys(clientDirMaps).join(", ")}`
    );
  }

  // 获取或克隆项目目录
  const gitDir = await getOrCloneProjectDir(appName, baseGithubDir);

  const result: any = {
    success: false,
    currentBranch: null,
    targetBranch: branchName,
    operations: [],
  };

  try {
    // Change to the git directory
    process.chdir(gitDir);
    Logger.log(`Changed directory to: ${gitDir}`);
    result.operations.push(`Changed to directory: ${gitDir}`);

    // Check if we're in a git repository
    execSync("git rev-parse --is-inside-work-tree", { stdio: "ignore" });

    // Show current branch status
    const currentBranch = getCurrentBranch();
    result.currentBranch = currentBranch;
    Logger.log(`Current branch: ${currentBranch}`);
    Logger.log(`Target branch: ${branchName}`);

    // 步骤2: 使用API检查远程分支是否存在
    Logger.log(`🔍 Checking if remote branch '${branchName}' exists...`);
    let remoteBranchExists = false;
    try {
      await getBranch({ appName, branchName });
      remoteBranchExists = true;
      Logger.log(`✓ Remote branch '${branchName}' exists`);
      result.operations.push(`Remote branch exists: ${branchName}`);
    } catch (error: any) {
      Logger.log(`Remote branch '${branchName}' does not exist`);
      result.operations.push(`Remote branch does not exist: ${branchName}`);
    }

    // 步骤3: 如果远程分支不存在，使用API创建分支
    if (!remoteBranchExists) {
      Logger.log(`🚀 Creating remote branch '${branchName}' from '${baseBranch}'...`);
      try {
        await createBranch({
          appName,
          branchName,
          targetBranchName: baseBranch,
        });
        Logger.log(`✅ Successfully created remote branch '${branchName}'`);
        result.operations.push(`Created remote branch: ${branchName} from ${baseBranch}`);
      } catch (error: any) {
        throw new Error(`Failed to create remote branch: ${error.message}`);
      }
    }

    // 步骤4: 如果需要更新包，先通过API更新远程package.json
    if (updatePackages) {
      Logger.log("\n🔄 Starting npm package update via API...");

      // Validate package parameters
      if (!packageName || !packageVersion) {
        throw new Error(
          "packageName and packageVersion are required when updatePackages is true"
        );
      }

      // 使用updatePackageJson API更新package.json
      Logger.log(`📦 Updating package.json via API...`);
      try {
        await updatePackageJson({
          appName,
          branchName,
          path: clientDir || undefined, // 确保空字符串转换为undefined
          packageInfo: {
            name: packageName,
            version: packageVersion,
          },
        });
        Logger.log(`✅ Successfully updated ${packageName} to ${packageVersion} via API`);
        result.operations.push(`Updated package via API: ${packageName}@${packageVersion}`);
      } catch (error: any) {
        throw new Error(`Failed to update package.json via API: ${error.message}`);
      }
    }

    // 步骤5: 执行本地操作 - git fetch, 切换分支, yarn, push
    Logger.log("\n🔄 Starting local operations...");

    // 确定工作目录路径
    const workingPath = clientDir ? path.join(gitDir, clientDir) : gitDir;

    // Check if we need to switch branches
    if (currentBranch === branchName) {
      Logger.log(`✓ Already on branch '${branchName}'`);
      if (isWorkingDirectoryClean()) {
        Logger.log("Working directory is clean, pulling latest changes...");
        execSync(`git pull origin ${branchName}`, { stdio: "inherit" });
        result.operations.push(
          `Pulled latest changes from origin/${branchName}`
        );
      } else if (cleanWorkingDir) {
        Logger.log("Working directory has changes, cleaning up first...");
        // Clean working directory first, then pull
        const status = execSync("git status --porcelain", { encoding: "utf8" });
        if (status.trim()) {
          Logger.log("Found uncommitted changes:");
          Logger.log(status);
          Logger.log("Cleaning up uncommitted changes...");

          // Reset any staged and unstaged changes
          execSync("git reset --hard HEAD", { stdio: "inherit" });
          Logger.log("✓ Reset all changes to last commit");
          result.operations.push("Reset uncommitted changes");

          // Remove untracked files and directories
          execSync("git clean -fd", { stdio: "inherit" });
          Logger.log("✓ Removed untracked files and directories");
          result.operations.push("Removed untracked files");
        }

        Logger.log("Pulling latest changes...");
        execSync(`git pull origin ${branchName}`, { stdio: "inherit" });
        result.operations.push(
          `Pulled latest changes from origin/${branchName}`
        );
      } else {
        Logger.log(
          "Working directory has changes, skipping pull (cleanWorkingDir=false)"
        );
        result.operations.push("Skipped pull due to uncommitted changes");
      }
    } else {
      // Check for uncommitted changes and clean them up
      if (cleanWorkingDir) {
        Logger.log("Checking for uncommitted changes...");
        try {
          const status = execSync("git status --porcelain", {
            encoding: "utf8",
          });
          if (status.trim()) {
            Logger.log("Found uncommitted changes:");
            Logger.log(status);
            Logger.log("Cleaning up uncommitted changes...");

            // Reset any staged and unstaged changes
            execSync("git reset --hard HEAD", { stdio: "inherit" });
            Logger.log("✓ Reset all changes to last commit");
            result.operations.push("Reset uncommitted changes");

            // Remove untracked files and directories
            execSync("git clean -fd", { stdio: "inherit" });
            Logger.log("✓ Removed untracked files and directories");
            result.operations.push("Removed untracked files");

            Logger.log("✓ Working directory is now clean");
          } else {
            Logger.log("✓ Working directory is already clean");
          }
        } catch (error) {
          Logger.log("Warning: Could not check git status, continuing...");
        }
      }

      // 执行git fetch，切换到目标分支
      Logger.log("📥 Fetching latest changes...");
      execSync("git fetch", { stdio: "inherit" });
      result.operations.push("Fetched latest changes");

      // 切换到目标分支
      Logger.log(`🔄 Switching to branch '${branchName}'...`);
      try {
        execSync(`git checkout ${branchName}`, { stdio: "inherit" });
        result.operations.push(`Checked out branch: ${branchName}`);

        // 拉取最新更改
        Logger.log("Pulling latest changes...");
        execSync(`git pull origin ${branchName}`, { stdio: "inherit" });
        result.operations.push(`Pulled latest changes from origin/${branchName}`);
      } catch (error: any) {
        // 如果本地分支不存在，从远程分支创建
        Logger.log(`Local branch doesn't exist, creating from origin/${branchName}...`);
        execSync(`git checkout -b ${branchName} origin/${branchName}`, { stdio: "inherit" });
        result.operations.push(`Created local branch from origin/${branchName}`);
      }
    }

    // 步骤6: 执行yarn安装 (切换分支后总是执行)
    Logger.log("\n📦 Installing dependencies...");

    // Check if working directory exists
    if (!fs.existsSync(workingPath)) {
      throw new Error(`Working directory not found: ${workingPath}`);
    }

    Logger.log(`Entering working directory: ${workingPath}`);
    process.chdir(workingPath);
    result.operations.push(`Changed to working directory: ${workingPath}`);

    // 执行yarn安装
    Logger.log(`📦 Running yarn install in: ${workingPath}`);
    execSync("yarn install", { stdio: "inherit" });
    result.operations.push(`Executed yarn install in: ${workingPath}`);

    Logger.log("✅ Dependencies installed successfully!");

    // 步骤7: 如果更新了包，需要重新执行yarn来更新yarn.lock
    if (updatePackages) {
      Logger.log(`\n📦 Re-running yarn install to update yarn.lock after API update...`);
      process.chdir(workingPath);
      execSync("yarn install", { stdio: "inherit" });
      result.operations.push(`Re-executed yarn install in: ${workingPath}`);
      Logger.log("✅ yarn.lock updated successfully!");
    }

    // 步骤8: 提交并推送更改
    if (updatePackages && commitAndPush) {
      Logger.log("\n📤 Pushing local changes...");

      // Go back to git root directory
      process.chdir(gitDir);

      // 由于package.json已经通过API更新，我们只需要拉取最新更改并推送yarn.lock
      Logger.log("📥 Fetching latest changes from remote...");
      execSync("git fetch", { stdio: "inherit" });
      execSync(`git pull origin ${branchName}`, { stdio: "inherit" });
      result.operations.push("Pulled latest changes including API updates");

      // Add yarn.lock changes (package.json已经通过API更新)
      const yarnLockPath = clientDir ? `${clientDir}/yarn.lock` : "yarn.lock";
      try {
        execSync(`git add ${yarnLockPath}`, { stdio: "inherit" });
        Logger.log("Added yarn.lock to git");
        result.operations.push("Added yarn.lock to git");

        // Check if there are changes to commit
        const status = execSync("git status --porcelain", {
          encoding: "utf8",
        });
        if (status.trim()) {
          // Generate commit message for yarn.lock
          const lockCommitMessage = commitMessage || `chore: update yarn.lock for ${packageName}@${packageVersion}`;

          // Commit changes
          execSync(`git commit -m "${lockCommitMessage}"`, {
            stdio: "inherit",
          });
          Logger.log(`Committed yarn.lock changes: ${lockCommitMessage}`);
          result.operations.push(`Committed: ${lockCommitMessage}`);

          // Push to remote
          execSync(`git push origin ${branchName}`, { stdio: "inherit" });
          Logger.log(`✅ Successfully pushed yarn.lock to origin/${branchName}`);
          result.operations.push(`Pushed yarn.lock to origin/${branchName}`);
        } else {
          Logger.log("No yarn.lock changes to commit");
          result.operations.push("No yarn.lock changes to commit");
        }
      } catch (error) {
        Logger.log("Warning: Could not add yarn.lock or no yarn.lock file found");
        result.operations.push("Warning: yarn.lock handling failed");
      }
    }

    result.success = true;
    return result;
  } catch (error: any) {
    result.error = error.message;
    throw error;
  }
}

// 导出函数供其他模块使用
export {
  createOrSwitchBranch,
  getCurrentBranch,
  isWorkingDirectoryClean,
  getOrCloneProjectDir,
};

export default {
  createOrSwitchBranch,
  getCurrentBranch,
  isWorkingDirectoryClean,
  getOrCloneProjectDir,
};
