import { createOrSwitchBranch } from "./gitbash.js";
import { packageAppMaps } from "./constants.js";
import { Logger } from "~/utils/logger.js";

export default async function (publishConfig: any): Promise<any> {
  const { modules, branch_name } = publishConfig;

  // 验证分支
  if (branch_name === "master") {
    Logger.log("Skipping master branch");
    return { success: false, error: "Master branch not allowed" };
  }

  // 验证 modules 是否存在
  if (!modules || !Array.isArray(modules) || modules.length === 0) {
    Logger.log("No modules found in data");
    return { success: false, error: "No modules found" };
  }

  Logger.log(`Processing ${modules.length} modules...`);

  const results: Array<{
    module_name: string;
    version: string;
    appName: string;
    success: boolean;
    operations?: any[];
    error?: string;
  }> = [];

  try {
    // 遍历每个 module
    for (const module of modules) {
      const { module_name, version } = module;

      Logger.log(`\n📦 Processing module: ${module_name}@${version}`);

      // 检查 module_name 是否在 packageAppMaps 中
      if (!packageAppMaps[module_name]) {
        Logger.log(
          `⏭️  Module ${module_name} not found in packageAppMaps, skipping...`
        );
        continue;
      }

      const targetApps = packageAppMaps[module_name];
      Logger.log(
        `🎯 Found ${targetApps.length} target apps: ${targetApps.join(", ")}`
      );

      // 为每个目标应用执行 createOrSwitchBranch
      for (const appName of targetApps) {
        Logger.log(`\n🚀 Processing app: ${appName}`);

        const options = {
          appName: appName,
          branchName: branch_name,
          updatePackages: true,
          packageName: module_name,
          packageVersion: version,
          clientDir: "client",
          commitAndPush: true,
          commitMessage: `feat: update ${module_name} to ${version}`,
        };

        try {
          const result = await createOrSwitchBranch(options);
          Logger.log(
            `✅ Successfully processed ${appName} for ${module_name}`
          );

          results.push({
            module_name,
            version,
            appName,
            success: true,
            operations: result.operations,
          });
        } catch (error) {
          console.error(
            `❌ Failed to process ${appName} for ${module_name}: ${error.message}`
          );

          results.push({
            module_name,
            version,
            appName,
            success: false,
            error: error.message,
          });
        }
      }
    }

    // 检查是否所有操作都成功
    const failedResults = results.filter((r) => !r.success);
    const successCount = results.filter((r) => r.success).length;

    Logger.log(
      `\n📊 Summary: ${successCount}/${results.length} operations completed successfully`
    );

    if (failedResults.length > 0) {
      Logger.log("❌ Failed operations:");
      failedResults.forEach((result) => {
        Logger.log(
          `   - ${result.appName} (${result.module_name}): ${result.error}`
        );
      });
    }

    return {
      success: failedResults.length === 0,
      totalModules: modules.length,
      processedOperations: results.length,
      successfulOperations: successCount,
      failedOperations: failedResults.length,
      results: results,
    };
  } catch (error) {
    console.error(`❌ Unexpected error during processing: ${error.message}`);
    return {
      success: false,
      error: error.message,
      results: results,
    };
  }
}
