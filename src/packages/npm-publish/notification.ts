import { execSync } from "child_process";

// 获取git配置的用户名
function getGitUserName(): string {
  try {
    return execSync("git config user.name", { encoding: "utf8" }).trim();
  } catch (error: any) {
    console.warn("Failed to get git user name:", error.message);
    return "Unknown User";
  }
}

// 生成通知文本
function generateNotificationText(result: any, originalData: any): string {
  const { branch_name } = originalData;
  const user_name = getGitUserName();

  // 如果没有 results 数组，说明是早期失败（用户验证、分支验证等）
  if (!result.success && (!result.results || result.results.length === 0)) {
    return `❌ NPM包更新失败\n用户: ${user_name}\n分支: ${branch_name}\n错误: ${result.error || "未知错误"}`;
  }

  const { successfulOperations, failedOperations, results } = result;

  // 成功的更新
  const successResults = results.filter((r: any) => r.success);
  const failedResults = results.filter((r: any) => !r.success);

  // 根据整体结果决定标题
  const title = result.success ? "✅ NPM包更新完成" : "⚠️ NPM包更新部分完成";

  let text = `${title}\n`;
  text += `用户: ${user_name}\n`;
  text += `分支: ${branch_name}\n`;
  text += `总计: ${successfulOperations}成功 / ${failedOperations}失败\n\n`;

  if (successResults.length > 0) {
    text += `📦 成功更新的包:\n`;
    successResults.forEach((result: any) => {
      text += `  • ${result.module_name}@${result.version} → ${result.appName}\n`;
    });
  }

  if (failedResults.length > 0) {
    text += `\n❌ 失败的更新:\n`;
    failedResults.forEach((result: any) => {
      text += `  • ${result.module_name}@${result.version} → ${result.appName}\n`;
      text += `    错误: ${result.error}\n`;
    });
  }

  return text;
}

export { generateNotificationText, getGitUserName };

export default {
  generateNotificationText,
  getGitUserName,
};
