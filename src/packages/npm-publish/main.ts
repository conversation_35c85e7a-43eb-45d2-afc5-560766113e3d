import task from "./task.js";
import { notify } from "../../utils/webhook.js"; // 引入通知函数
import { generateNotificationText } from "./notification.js";
import { Logger } from "~/utils/logger.js";

/**
 * 
 * @param {*} mcpOptions 
 * {
 *  npmName: "@youzan/order-domain-pc-components",
    npmVersion: "1.1.5-beta.20250627164612.0",
    branch: "feature/test-111",
 * }
 */
async function main(mcpOptions: any) {
  // 格式转化
  const publishConfig = {
    branch_name: mcpOptions.branch,
    modules: [
      {
        module_name: mcpOptions.npmName,
        version: mcpOptions.npmVersion,
      },
    ],
  };

  try {
    Logger.log("Received npm publish data:", publishConfig);
    const result = await task(publishConfig);
    Logger.log("NPM upgrade result:", result);

    // 生成通知文本
    const notificationText = generateNotificationText(result, publishConfig);

    // 发送通知
    notify({
      msg_type: "text",
      content: {
        text: notificationText,
      },
    });
    return notificationText;
  } catch (error: any) {
    console.error("Error processing npm publish:", error);
    const errorText = `❌ NPM包更新处理失败\n错误: ${
          error.message
        }\n数据: ${JSON.stringify(publishConfig)}`;
    // 发送错误通知
    notify({
      msg_type: "text",
      content: {
        text: errorText,
      },
    });
    return errorText;
  }
}

export default main;
