[{"id": 1414, "description": "微信小程序之微商城，这个代码不要泄露到外部，可能有安全风险", "name": "wsc", "name_with_namespace": "weapp / wsc", "path": "wsc", "path_with_namespace": "weapp/wsc", "created_at": "2016-11-16T01:50:03.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:weapp/wsc.git", "http_url_to_repo": "https://gitlab.qima-inc.com/weapp/wsc.git", "web_url": "https://gitlab.qima-inc.com/weapp/wsc", "readme_url": "https://gitlab.qima-inc.com/weapp/wsc/-/blob/master/README.md", "avatar_url": "https://gitlab.qima-inc.com/uploads/-/system/project/avatar/1414/WechatIMG3.jpeg", "forks_count": 2, "star_count": 45, "last_activity_at": "2025-07-12T12:28:42.617Z", "namespace": {"id": 356, "name": "weapp", "path": "weapp", "kind": "group", "full_path": "weapp", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg", "web_url": "https://gitlab.qima-inc.com/groups/weapp"}}, {"id": 11795, "description": "", "name": "wsc-tee-h5", "name_with_namespace": "weapp / wsc-tee-h5", "path": "wsc-tee-h5", "path_with_namespace": "weapp/wsc-tee-h5", "created_at": "2021-03-30T12:15:17.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:weapp/wsc-tee-h5.git", "http_url_to_repo": "https://gitlab.qima-inc.com/weapp/wsc-tee-h5.git", "web_url": "https://gitlab.qima-inc.com/weapp/wsc-tee-h5", "readme_url": "https://gitlab.qima-inc.com/weapp/wsc-tee-h5/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 9, "last_activity_at": "2025-07-12T12:28:38.823Z", "namespace": {"id": 356, "name": "weapp", "path": "weapp", "kind": "group", "full_path": "weapp", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg", "web_url": "https://gitlab.qima-inc.com/groups/weapp"}}, {"id": 12047, "description": "", "name": "ext-tee-wsc-trade", "name_with_namespace": "weapp / ext-tee-wsc-trade", "path": "ext-tee-wsc-trade", "path_with_namespace": "weapp/ext-tee-wsc-trade", "created_at": "2021-05-10T03:22:47.147Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:weapp/ext-tee-wsc-trade.git", "http_url_to_repo": "https://gitlab.qima-inc.com/weapp/ext-tee-wsc-trade.git", "web_url": "https://gitlab.qima-inc.com/weapp/ext-tee-wsc-trade", "readme_url": "https://gitlab.qima-inc.com/weapp/ext-tee-wsc-trade/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 5, "last_activity_at": "2025-07-12T12:28:38.016Z", "namespace": {"id": 356, "name": "weapp", "path": "weapp", "kind": "group", "full_path": "weapp", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg", "web_url": "https://gitlab.qima-inc.com/groups/weapp"}}, {"id": 11654, "description": "资产 - 多端中台化扩展仓库", "name": "ext-tee-assets", "name_with_namespace": "weapp / ext-tee-assets", "path": "ext-tee-assets", "path_with_namespace": "weapp/ext-tee-assets", "created_at": "2021-03-12T02:57:31.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:weapp/ext-tee-assets.git", "http_url_to_repo": "https://gitlab.qima-inc.com/weapp/ext-tee-assets.git", "web_url": "https://gitlab.qima-inc.com/weapp/ext-tee-assets", "readme_url": "https://gitlab.qima-inc.com/weapp/ext-tee-assets/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-12T12:28:37.314Z", "namespace": {"id": 356, "name": "weapp", "path": "weapp", "kind": "group", "full_path": "weapp", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg", "web_url": "https://gitlab.qima-inc.com/groups/weapp"}}, {"id": 11809, "description": "商品 - 多端中台化扩展仓库\r\n", "name": "ext-tee-wsc-goods", "name_with_namespace": "weapp / ext-tee-wsc-goods", "path": "ext-tee-wsc-goods", "path_with_namespace": "weapp/ext-tee-wsc-goods", "created_at": "2021-04-01T06:24:03.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:weapp/ext-tee-wsc-goods.git", "http_url_to_repo": "https://gitlab.qima-inc.com/weapp/ext-tee-wsc-goods.git", "web_url": "https://gitlab.qima-inc.com/weapp/ext-tee-wsc-goods", "readme_url": "https://gitlab.qima-inc.com/weapp/ext-tee-wsc-goods/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 3, "last_activity_at": "2025-07-12T12:28:36.608Z", "namespace": {"id": 356, "name": "weapp", "path": "weapp", "kind": "group", "full_path": "weapp", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg", "web_url": "https://gitlab.qima-inc.com/groups/weapp"}}, {"id": 15409, "description": "", "name": "npmpublish", "name_with_namespace": "xujiazheng / npmpublish", "path": "npmpublish", "path_with_namespace": "xujiazheng/npmpublish", "created_at": "2025-06-17T07:09:53.775Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:xujiazheng/npmpublish.git", "http_url_to_repo": "https://gitlab.qima-inc.com/xujiazheng/npmpublish.git", "web_url": "https://gitlab.qima-inc.com/xujiazheng/npmpublish", "readme_url": "https://gitlab.qima-inc.com/xujiazheng/npmpublish/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-12T06:09:29.454Z", "namespace": {"id": 3020, "name": "xu<PERSON>azheng", "path": "xu<PERSON>azheng", "kind": "user", "full_path": "xu<PERSON>azheng", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/xujiazheng"}}, {"id": 10345, "description": "零售 PC 后台履约模块", "name": "retail-node-fulfillment", "name_with_namespace": "retail-web / retail-node-fulfillment", "path": "retail-node-fulfillment", "path_with_namespace": "retail-web/retail-node-fulfillment", "created_at": "2020-08-20T03:40:06.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:retail-web/retail-node-fulfillment.git", "http_url_to_repo": "https://gitlab.qima-inc.com/retail-web/retail-node-fulfillment.git", "web_url": "https://gitlab.qima-inc.com/retail-web/retail-node-fulfillment", "readme_url": "https://gitlab.qima-inc.com/retail-web/retail-node-fulfillment/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 1, "last_activity_at": "2025-07-12T05:47:42.893Z", "namespace": {"id": 579, "name": "retail-web", "path": "retail-web", "kind": "group", "full_path": "retail-web", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/579/box-packing-peanut-512-min.png", "web_url": "https://gitlab.qima-inc.com/groups/retail-web"}}, {"id": 5808, "description": "", "name": "retail-node-order", "name_with_namespace": "retail-web / retail-node-order", "path": "retail-node-order", "path_with_namespace": "retail-web/retail-node-order", "created_at": "2018-10-10T02:20:24.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:retail-web/retail-node-order.git", "http_url_to_repo": "https://gitlab.qima-inc.com/retail-web/retail-node-order.git", "web_url": "https://gitlab.qima-inc.com/retail-web/retail-node-order", "readme_url": "https://gitlab.qima-inc.com/retail-web/retail-node-order/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 4, "last_activity_at": "2025-07-12T05:47:28.590Z", "namespace": {"id": 579, "name": "retail-web", "path": "retail-web", "kind": "group", "full_path": "retail-web", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/579/box-packing-peanut-512-min.png", "web_url": "https://gitlab.qima-inc.com/groups/retail-web"}}, {"id": 4950, "description": "Iron PC 拆分业务：订单", "name": "wsc-pc-trade", "name_with_namespace": "wsc-node / wsc-pc-trade", "path": "wsc-pc-trade", "path_with_namespace": "wsc-node/wsc-pc-trade", "created_at": "2018-06-11T03:19:44.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/wsc-pc-trade.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-trade.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-trade", "readme_url": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-trade/-/blob/master/README.md", "avatar_url": null, "forks_count": 1, "star_count": 6, "last_activity_at": "2025-07-12T05:46:17.136Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 11108, "description": "储值pc", "name": "retail-pc-prepaid", "name_with_namespace": "retail-web / retail-pc-prepaid", "path": "retail-pc-prepaid", "path_with_namespace": "retail-web/retail-pc-prepaid", "created_at": "2020-12-16T03:42:43.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:retail-web/retail-pc-prepaid.git", "http_url_to_repo": "https://gitlab.qima-inc.com/retail-web/retail-pc-prepaid.git", "web_url": "https://gitlab.qima-inc.com/retail-web/retail-pc-prepaid", "readme_url": "https://gitlab.qima-inc.com/retail-web/retail-pc-prepaid/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 2, "last_activity_at": "2025-07-12T03:15:22.684Z", "namespace": {"id": 579, "name": "retail-web", "path": "retail-web", "kind": "group", "full_path": "retail-web", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/579/box-packing-peanut-512-min.png", "web_url": "https://gitlab.qima-inc.com/groups/retail-web"}}, {"id": 11109, "description": "储值H5", "name": "retail-h5-prepaid", "name_with_namespace": "retail-web / retail-h5-prepaid", "path": "retail-h5-prepaid", "path_with_namespace": "retail-web/retail-h5-prepaid", "created_at": "2020-12-16T03:45:15.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:retail-web/retail-h5-prepaid.git", "http_url_to_repo": "https://gitlab.qima-inc.com/retail-web/retail-h5-prepaid.git", "web_url": "https://gitlab.qima-inc.com/retail-web/retail-h5-prepaid", "readme_url": "https://gitlab.qima-inc.com/retail-web/retail-h5-prepaid/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 2, "last_activity_at": "2025-07-12T00:05:12.340Z", "namespace": {"id": 579, "name": "retail-web", "path": "retail-web", "kind": "group", "full_path": "retail-web", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/579/box-packing-peanut-512-min.png", "web_url": "https://gitlab.qima-inc.com/groups/retail-web"}}, {"id": 10002, "description": "多端框架", "name": "tee", "name_with_namespace": "fe-middle-platform / tee", "path": "tee", "path_with_namespace": "fe-middle-platform/tee", "created_at": "2020-07-08T02:32:42.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe-middle-platform/tee.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe-middle-platform/tee.git", "web_url": "https://gitlab.qima-inc.com/fe-middle-platform/tee", "readme_url": "https://gitlab.qima-inc.com/fe-middle-platform/tee/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 3, "last_activity_at": "2025-07-12T00:05:11.306Z", "namespace": {"id": 2081, "name": "fe-middle-platform", "path": "fe-middle-platform", "kind": "group", "full_path": "fe-middle-platform", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/2081/images.png", "web_url": "https://gitlab.qima-inc.com/groups/fe-middle-platform"}}, {"id": 10075, "description": "多端小程序 base 仓库", "name": "wsc-tee-base", "name_with_namespace": "weapp / wsc-tee-base", "path": "wsc-tee-base", "path_with_namespace": "weapp/wsc-tee-base", "created_at": "2020-07-16T02:33:09.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:weapp/wsc-tee-base.git", "http_url_to_repo": "https://gitlab.qima-inc.com/weapp/wsc-tee-base.git", "web_url": "https://gitlab.qima-inc.com/weapp/wsc-tee-base", "readme_url": "https://gitlab.qima-inc.com/weapp/wsc-tee-base/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 1, "last_activity_at": "2025-07-12T00:05:10.718Z", "namespace": {"id": 356, "name": "weapp", "path": "weapp", "kind": "group", "full_path": "weapp", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg", "web_url": "https://gitlab.qima-inc.com/groups/weapp"}}, {"id": 6197, "description": "零售前端（商品） Node.js 项目", "name": "retail-node-goods", "name_with_namespace": "retail-web / retail-node-goods", "path": "retail-node-goods", "path_with_namespace": "retail-web/retail-node-goods", "created_at": "2018-12-05T02:20:41.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:retail-web/retail-node-goods.git", "http_url_to_repo": "https://gitlab.qima-inc.com/retail-web/retail-node-goods.git", "web_url": "https://gitlab.qima-inc.com/retail-web/retail-node-goods", "readme_url": "https://gitlab.qima-inc.com/retail-web/retail-node-goods/-/blob/master/README.md", "avatar_url": "https://gitlab.qima-inc.com/uploads/-/system/project/avatar/6197/ls-logo_4.jpg", "forks_count": 0, "star_count": 5, "last_activity_at": "2025-07-12T00:05:07.920Z", "namespace": {"id": 579, "name": "retail-web", "path": "retail-web", "kind": "group", "full_path": "retail-web", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/579/box-packing-peanut-512-min.png", "web_url": "https://gitlab.qima-inc.com/groups/retail-web"}}, {"id": 6081, "description": "零售前端店铺 Node 项目", "name": "retail-node-shop", "name_with_namespace": "retail-web / retail-node-shop", "path": "retail-node-shop", "path_with_namespace": "retail-web/retail-node-shop", "created_at": "2018-11-21T12:00:11.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:retail-web/retail-node-shop.git", "http_url_to_repo": "https://gitlab.qima-inc.com/retail-web/retail-node-shop.git", "web_url": "https://gitlab.qima-inc.com/retail-web/retail-node-shop", "readme_url": "https://gitlab.qima-inc.com/retail-web/retail-node-shop/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 3, "last_activity_at": "2025-07-12T00:05:06.181Z", "namespace": {"id": 579, "name": "retail-web", "path": "retail-web", "kind": "group", "full_path": "retail-web", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/579/box-packing-peanut-512-min.png", "web_url": "https://gitlab.qima-inc.com/groups/retail-web"}}, {"id": 6184, "description": "一些小的业务模块，目前包含 dashboard tool minapp", "name": "retail-node-v2", "name_with_namespace": "retail-web / retail-node-v2", "path": "retail-node-v2", "path_with_namespace": "retail-web/retail-node-v2", "created_at": "2018-12-04T02:59:02.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:retail-web/retail-node-v2.git", "http_url_to_repo": "https://gitlab.qima-inc.com/retail-web/retail-node-v2.git", "web_url": "https://gitlab.qima-inc.com/retail-web/retail-node-v2", "readme_url": "https://gitlab.qima-inc.com/retail-web/retail-node-v2/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-12T00:05:05.715Z", "namespace": {"id": 579, "name": "retail-web", "path": "retail-web", "kind": "group", "full_path": "retail-web", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/579/box-packing-peanut-512-min.png", "web_url": "https://gitlab.qima-inc.com/groups/retail-web"}}, {"id": 5136, "description": "Iron H5 业务拆分：资产", "name": "wsc-h5-assets", "name_with_namespace": "wsc-node / wsc-h5-assets", "path": "wsc-h5-assets", "path_with_namespace": "wsc-node/wsc-h5-assets", "created_at": "2018-07-11T03:43:04.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/wsc-h5-assets.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-assets.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-assets", "readme_url": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-assets/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 1, "last_activity_at": "2025-07-11T22:00:01.712Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 6575, "description": "统一收银台项目", "name": "assets-cashier-source", "name_with_namespace": "fe-assets / assets-cashier-source", "path": "assets-cashier-source", "path_with_namespace": "fe-assets/assets-cashier-source", "created_at": "2019-01-16T09:33:54.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe-assets/assets-cashier-source.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe-assets/assets-cashier-source.git", "web_url": "https://gitlab.qima-inc.com/fe-assets/assets-cashier-source", "readme_url": "https://gitlab.qima-inc.com/fe-assets/assets-cashier-source/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-11T22:00:01.394Z", "namespace": {"id": 1442, "name": "fe-assets", "path": "fe-assets", "kind": "group", "full_path": "fe-assets", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/fe-assets"}}, {"id": 4953, "description": "", "name": "cert", "name_with_namespace": "fe / cert", "path": "cert", "path_with_namespace": "fe/cert", "created_at": "2018-06-11T06:19:23.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/cert.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/cert.git", "web_url": "https://gitlab.qima-inc.com/fe/cert", "readme_url": "https://gitlab.qima-inc.com/fe/cert/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-11T22:00:01.219Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 5243, "description": "对账平台，前端应用", "name": "pay-check-web", "name_with_namespace": "fe / pay-check-web", "path": "pay-check-web", "path_with_namespace": "fe/pay-check-web", "created_at": "2018-07-25T02:33:38.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/pay-check-web.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/pay-check-web.git", "web_url": "https://gitlab.qima-inc.com/fe/pay-check-web", "readme_url": "https://gitlab.qima-inc.com/fe/pay-check-web/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-11T22:00:00.901Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 4949, "description": "Iron PC 拆分业务：商品", "name": "wsc-pc-goods", "name_with_namespace": "wsc-node / wsc-pc-goods", "path": "wsc-pc-goods", "path_with_namespace": "wsc-node/wsc-pc-goods", "created_at": "2018-06-11T03:15:40.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/wsc-pc-goods.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-goods.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-goods", "readme_url": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-goods/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 7, "last_activity_at": "2025-07-11T16:49:43.992Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 15347, "description": "有赞碰碰贴小程序（支付宝，微信）项目", "name": "zan-ppt", "name_with_namespace": "weapp / zan-ppt", "path": "zan-ppt", "path_with_namespace": "weapp/zan-ppt", "created_at": "2025-05-30T08:54:27.060Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:weapp/zan-ppt.git", "http_url_to_repo": "https://gitlab.qima-inc.com/weapp/zan-ppt.git", "web_url": "https://gitlab.qima-inc.com/weapp/zan-ppt", "readme_url": "https://gitlab.qima-inc.com/weapp/zan-ppt/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-11T10:42:32.968Z", "namespace": {"id": 356, "name": "weapp", "path": "weapp", "kind": "group", "full_path": "weapp", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg", "web_url": "https://gitlab.qima-inc.com/groups/weapp"}}, {"id": 11993, "description": "提供 SCRM 公用能力的 NPM 包", "name": "Scrm Packages Mono", "name_with_namespace": "fe / Scrm Packages Mono", "path": "scrm-packages-mono", "path_with_namespace": "fe/scrm-packages-mono", "created_at": "2021-04-27T02:04:28.375Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/scrm-packages-mono.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/scrm-packages-mono.git", "web_url": "https://gitlab.qima-inc.com/fe/scrm-packages-mono", "readme_url": "https://gitlab.qima-inc.com/fe/scrm-packages-mono/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-11T10:28:47.173Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 10798, "description": "有赞 SCRM B 端发布仓库。\r\n注意：不是源码仓库，仅用于发布。", "name": "scrm-b-pc-dist", "name_with_namespace": "fe / scrm-b-pc-dist", "path": "scrm-b-pc-dist", "path_with_namespace": "fe/scrm-b-pc-dist", "created_at": "2020-10-29T07:31:27.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/scrm-b-pc-dist.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/scrm-b-pc-dist.git", "web_url": "https://gitlab.qima-inc.com/fe/scrm-b-pc-dist", "readme_url": null, "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-11T10:24:33.509Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 4947, "description": "Iron PC 拆分业务：概况、店铺", "name": "wsc-pc-shop", "name_with_namespace": "wsc-node / wsc-pc-shop", "path": "wsc-pc-shop", "path_with_namespace": "wsc-node/wsc-pc-shop", "created_at": "2018-06-11T03:07:57.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/wsc-pc-shop.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-shop.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-shop", "readme_url": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-shop/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 7, "last_activity_at": "2025-07-11T10:06:12.352Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 14787, "description": "", "name": "<PERSON><PERSON>", "name_with_namespace": "fe / <PERSON>i <PERSON>", "path": "ci_cache", "path_with_namespace": "fe/ci_cache", "created_at": "2023-08-29T08:54:43.183Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/ci_cache.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/ci_cache.git", "web_url": "https://gitlab.qima-inc.com/fe/ci_cache", "readme_url": "https://gitlab.qima-inc.com/fe/ci_cache/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-11T09:53:33.282Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 10775, "description": "有赞 SCRM monorepo", "name": "scrm-mono", "name_with_namespace": "fe / scrm-mono", "path": "scrm-mono", "path_with_namespace": "fe/scrm-mono", "created_at": "2020-10-27T09:43:31.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/scrm-mono.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/scrm-mono.git", "web_url": "https://gitlab.qima-inc.com/fe/scrm-mono", "readme_url": "https://gitlab.qima-inc.com/fe/scrm-mono/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 5, "last_activity_at": "2025-07-11T09:51:01.617Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 4585, "description": "The multi package repo for IM project", "name": "im-web", "name_with_namespace": "fe / im-web", "path": "im-web", "path_with_namespace": "fe/im-web", "created_at": "2018-04-18T06:47:30.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/im-web.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/im-web.git", "web_url": "https://gitlab.qima-inc.com/fe/im-web", "readme_url": "https://gitlab.qima-inc.com/fe/im-web/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 3, "last_activity_at": "2025-07-11T09:48:58.847Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 15150, "description": "私域直播ext", "name": "ext-tee-live", "name_with_namespace": "private-live / ext-tee-live", "path": "ext-tee-live", "path_with_namespace": "private-live/ext-tee-live", "created_at": "2024-12-26T03:09:02.372Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:private-live/ext-tee-live.git", "http_url_to_repo": "https://gitlab.qima-inc.com/private-live/ext-tee-live.git", "web_url": "https://gitlab.qima-inc.com/private-live/ext-tee-live", "readme_url": "https://gitlab.qima-inc.com/private-live/ext-tee-live/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 1, "last_activity_at": "2025-07-11T09:10:01.776Z", "namespace": {"id": 3840, "name": "private-live", "path": "private-live", "kind": "group", "full_path": "private-live", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/private-live"}}, {"id": 10567, "description": "", "name": "decorate-tee", "name_with_namespace": "weapp / decorate-tee", "path": "decorate-tee", "path_with_namespace": "weapp/decorate-tee", "created_at": "2020-09-15T12:44:03.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:weapp/decorate-tee.git", "http_url_to_repo": "https://gitlab.qima-inc.com/weapp/decorate-tee.git", "web_url": "https://gitlab.qima-inc.com/weapp/decorate-tee", "readme_url": "https://gitlab.qima-inc.com/weapp/decorate-tee/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 2, "last_activity_at": "2025-07-11T08:53:13.443Z", "namespace": {"id": 356, "name": "weapp", "path": "weapp", "kind": "group", "full_path": "weapp", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg", "web_url": "https://gitlab.qima-inc.com/groups/weapp"}}, {"id": 14921, "description": "", "name": "fe-test-service", "name_with_namespace": "fe-middle-platform / fe-test-service", "path": "fe-test-service", "path_with_namespace": "fe-middle-platform/fe-test-service", "created_at": "2024-03-05T08:02:20.473Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe-middle-platform/fe-test-service.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe-middle-platform/fe-test-service.git", "web_url": "https://gitlab.qima-inc.com/fe-middle-platform/fe-test-service", "readme_url": "https://gitlab.qima-inc.com/fe-middle-platform/fe-test-service/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-11T08:29:13.788Z", "namespace": {"id": 2081, "name": "fe-middle-platform", "path": "fe-middle-platform", "kind": "group", "full_path": "fe-middle-platform", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/2081/images.png", "web_url": "https://gitlab.qima-inc.com/groups/fe-middle-platform"}}, {"id": 11628, "description": "微商城装修  - 多端中台化扩展仓库", "name": "ext-tee-wsc-decorate", "name_with_namespace": "weapp / ext-tee-wsc-decorate", "path": "ext-tee-wsc-decorate", "path_with_namespace": "weapp/ext-tee-wsc-decorate", "created_at": "2021-03-09T02:50:23.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:weapp/ext-tee-wsc-decorate.git", "http_url_to_repo": "https://gitlab.qima-inc.com/weapp/ext-tee-wsc-decorate.git", "web_url": "https://gitlab.qima-inc.com/weapp/ext-tee-wsc-decorate", "readme_url": "https://gitlab.qima-inc.com/weapp/ext-tee-wsc-decorate/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 1, "last_activity_at": "2025-07-11T08:26:21.465Z", "namespace": {"id": 356, "name": "weapp", "path": "weapp", "kind": "group", "full_path": "weapp", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg", "web_url": "https://gitlab.qima-inc.com/groups/weapp"}}, {"id": 12207, "description": "货架小程序跨端仓库", "name": "ext-tee-retail-shelf", "name_with_namespace": "retail-web / retail-tee / ext-tee-retail-shelf", "path": "ext-tee-retail-shelf", "path_with_namespace": "retail-web/retail-tee/ext-tee-retail-shelf", "created_at": "2021-06-04T08:29:50.598Z", "default_branch": "dev", "tag_list": [], "ssh_url_to_repo": "***********************:retail-web/retail-tee/ext-tee-retail-shelf.git", "http_url_to_repo": "https://gitlab.qima-inc.com/retail-web/retail-tee/ext-tee-retail-shelf.git", "web_url": "https://gitlab.qima-inc.com/retail-web/retail-tee/ext-tee-retail-shelf", "readme_url": "https://gitlab.qima-inc.com/retail-web/retail-tee/ext-tee-retail-shelf/-/blob/dev/README.md", "avatar_url": null, "forks_count": 0, "star_count": 2, "last_activity_at": "2025-07-11T08:24:33.817Z", "namespace": {"id": 2872, "name": "retail-tee", "path": "retail-tee", "kind": "group", "full_path": "retail-web/retail-tee", "parent_id": 579, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/retail-web/retail-tee"}}, {"id": 10755, "description": "企业微信助手B端 monorepo", "name": "weass-b-mono", "name_with_namespace": "fe / weass-b-mono", "path": "weass-b-mono", "path_with_namespace": "fe/weass-b-mono", "created_at": "2020-10-23T11:01:07.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/weass-b-mono.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/weass-b-mono.git", "web_url": "https://gitlab.qima-inc.com/fe/weass-b-mono", "readme_url": "https://gitlab.qima-inc.com/fe/weass-b-mono/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 4, "last_activity_at": "2025-07-11T08:22:27.003Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 5182, "description": "微商城消息相关业务源码仓库", "name": "wsc-pc-message-web", "name_with_namespace": "fe / wsc-pc-message-web", "path": "wsc-pc-message-web", "path_with_namespace": "fe/wsc-pc-message-web", "created_at": "2018-07-16T12:27:17.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/wsc-pc-message-web.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/wsc-pc-message-web.git", "web_url": "https://gitlab.qima-inc.com/fe/wsc-pc-message-web", "readme_url": "https://gitlab.qima-inc.com/fe/wsc-pc-message-web/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 2, "last_activity_at": "2025-07-11T08:21:11.263Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 14566, "description": "", "name": "jarvis-front", "name_with_namespace": "fe / jarvis-front", "path": "jarvis-front", "path_with_namespace": "fe/jarvis-front", "created_at": "2023-03-20T10:04:36.182Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/jarvis-front.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/jarvis-front.git", "web_url": "https://gitlab.qima-inc.com/fe/jarvis-front", "readme_url": "https://gitlab.qima-inc.com/fe/jarvis-front/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-11T08:15:40.876Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 12951, "description": "有赞云文档中心的文档源文件\r\n该仓库非有赞云文档中心的源代码", "name": "CloudDocs", "name_with_namespace": "youzan / CloudDocs", "path": "cloudDocs", "path_with_namespace": "youzan/cloudDocs", "created_at": "2021-10-09T07:35:56.865Z", "default_branch": "preview", "tag_list": [], "ssh_url_to_repo": "***********************:youzan/cloudDocs.git", "http_url_to_repo": "https://gitlab.qima-inc.com/youzan/cloudDocs.git", "web_url": "https://gitlab.qima-inc.com/youzan/cloudDocs", "readme_url": "https://gitlab.qima-inc.com/youzan/cloudDocs/-/blob/preview/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-11T08:11:11.697Z", "namespace": {"id": 5, "name": "you<PERSON>", "path": "you<PERSON>", "kind": "group", "full_path": "you<PERSON>", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/5/cda5c81e0c64c791c4c190c1efcc1eaa.png", "web_url": "https://gitlab.qima-inc.com/groups/youzan"}}, {"id": 9988, "description": "装修相关业务", "name": "wsc-h5-decorate", "name_with_namespace": "wsc-node / wsc-h5-decorate", "path": "wsc-h5-decorate", "path_with_namespace": "wsc-node/wsc-h5-decorate", "created_at": "2020-07-06T09:26:12.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/wsc-h5-decorate.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-decorate.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-decorate", "readme_url": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-decorate/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 4, "last_activity_at": "2025-07-11T08:01:19.422Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 15198, "description": "", "name": "ai-test", "name_with_namespace": "fe / ai-test", "path": "ai-test", "path_with_namespace": "fe/ai-test", "created_at": "2025-03-03T03:20:44.577Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/ai-test.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/ai-test.git", "web_url": "https://gitlab.qima-inc.com/fe/ai-test", "readme_url": "https://gitlab.qima-inc.com/fe/ai-test/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-11T08:00:26.005Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 6796, "description": "", "name": "wsc-h5-showcase-components", "name_with_namespace": "fe / wsc-h5-showcase-components", "path": "wsc-h5-showcase-components", "path_with_namespace": "fe/wsc-h5-showcase-components", "created_at": "2019-02-28T09:57:05.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/wsc-h5-showcase-components.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/wsc-h5-showcase-components.git", "web_url": "https://gitlab.qima-inc.com/fe/wsc-h5-showcase-components", "readme_url": "https://gitlab.qima-inc.com/fe/wsc-h5-showcase-components/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 3, "last_activity_at": "2025-07-11T07:55:21.029Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 15200, "description": "", "name": "ai-test-chrome-extension", "name_with_namespace": "fe / ai-test-chrome-extension", "path": "ai-test-chrome-extension", "path_with_namespace": "fe/ai-test-chrome-extension", "created_at": "2025-03-03T10:55:02.824Z", "default_branch": "main", "tag_list": [], "ssh_url_to_repo": "***********************:fe/ai-test-chrome-extension.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/ai-test-chrome-extension.git", "web_url": "https://gitlab.qima-inc.com/fe/ai-test-chrome-extension", "readme_url": "https://gitlab.qima-inc.com/fe/ai-test-chrome-extension/-/blob/main/README.md", "avatar_url": "https://gitlab.qima-inc.com/uploads/-/system/project/avatar/15200/20250319-173543.png", "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-11T07:54:16.638Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 14597, "description": "", "name": "*********************", "name_with_namespace": "fe / *********************", "path": "*********************", "path_with_namespace": "fe/*********************", "created_at": "2023-04-19T09:16:07.046Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/*********************.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/*********************.git", "web_url": "https://gitlab.qima-inc.com/fe/*********************", "readme_url": null, "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-11T07:41:21.045Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 14589, "description": "", "name": "jarvis-commander-mono", "name_with_namespace": "fe / jarvis-commander-mono", "path": "jarvis-commander-mono", "path_with_namespace": "fe/jarvis-commander-mono", "created_at": "2023-04-13T02:19:27.964Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/jarvis-commander-mono.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/jarvis-commander-mono.git", "web_url": "https://gitlab.qima-inc.com/fe/jarvis-commander-mono", "readme_url": "https://gitlab.qima-inc.com/fe/jarvis-commander-mono/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 1, "last_activity_at": "2025-07-11T07:33:31.326Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 4576, "description": "Iron H5 拆分业务：应用营销", "name": "wsc-h5-ump", "name_with_namespace": "wsc-node / wsc-h5-ump", "path": "wsc-h5-ump", "path_with_namespace": "wsc-node/wsc-h5-ump", "created_at": "2018-04-17T07:10:05.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/wsc-h5-ump.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-ump.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-ump", "readme_url": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-ump/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 3, "last_activity_at": "2025-07-11T07:30:44.295Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 14448, "description": "营销画布前端仓库", "name": "ma-front", "name_with_namespace": "fe / ma-front", "path": "ma-front", "path_with_namespace": "fe/ma-front", "created_at": "2022-11-30T06:02:24.470Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/ma-front.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/ma-front.git", "web_url": "https://gitlab.qima-inc.com/fe/ma-front", "readme_url": "https://gitlab.qima-inc.com/fe/ma-front/-/blob/master/README.md", "avatar_url": "https://gitlab.qima-inc.com/uploads/-/system/project/avatar/14448/00000105.jpg", "forks_count": 0, "star_count": 1, "last_activity_at": "2025-07-11T07:26:44.775Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 11520, "description": "", "name": "guide-b-h5", "name_with_namespace": "wsc-node / guide-b-h5", "path": "guide-b-h5", "path_with_namespace": "wsc-node/guide-b-h5", "created_at": "2021-02-19T03:58:17.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/guide-b-h5.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/guide-b-h5.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/guide-b-h5", "readme_url": "https://gitlab.qima-inc.com/wsc-node/guide-b-h5/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 3, "last_activity_at": "2025-07-11T07:18:43.591Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 4555, "description": "Iron H5 拆分业务：微页面、商品、店铺", "name": "wsc-h5-shop", "name_with_namespace": "wsc-node / wsc-h5-shop", "path": "wsc-h5-shop", "path_with_namespace": "wsc-node/wsc-h5-shop", "created_at": "2018-04-13T02:22:55.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/wsc-h5-shop.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-shop.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-shop", "readme_url": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-shop/-/blob/master/README.md", "avatar_url": null, "forks_count": 1, "star_count": 8, "last_activity_at": "2025-07-11T07:16:16.403Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 4573, "description": "Iron 拆分业务：交易、下单、订单", "name": "wsc-h5-trade", "name_with_namespace": "wsc-node / wsc-h5-trade", "path": "wsc-h5-trade", "path_with_namespace": "wsc-node/wsc-h5-trade", "created_at": "2018-04-17T03:20:29.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/wsc-h5-trade.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-trade.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-trade", "readme_url": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-trade/-/blob/master/README.md", "avatar_url": "https://gitlab.qima-inc.com/uploads/-/system/project/avatar/4573/Node.js__2_.png", "forks_count": 0, "star_count": 13, "last_activity_at": "2025-07-11T07:01:47.350Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 8911, "description": "分销员，云分销 消费者端业务", "name": "wsc-h5-salesman", "name_with_namespace": "wsc-node / wsc-h5-salesman", "path": "wsc-h5-salesman", "path_with_namespace": "wsc-node/wsc-h5-salesman", "created_at": "2020-01-17T03:51:32.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/wsc-h5-salesman.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-salesman.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-salesman", "readme_url": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-salesman/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 4, "last_activity_at": "2025-07-11T06:53:45.475Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 5046, "description": "Iron PC 拆分业务：SCRM", "name": "wsc-pc-scrm", "name_with_namespace": "wsc-node / wsc-pc-scrm", "path": "wsc-pc-scrm", "path_with_namespace": "wsc-node/wsc-pc-scrm", "created_at": "2018-06-27T07:40:55.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/wsc-pc-scrm.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-scrm.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-scrm", "readme_url": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-scrm/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 5, "last_activity_at": "2025-07-11T06:44:15.482Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 1979, "description": "微商城React业务组件", "name": "react-components", "name_with_namespace": "fe / react-components", "path": "react-components", "path_with_namespace": "fe/react-components", "created_at": "2017-02-21T09:36:48.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/react-components.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/react-components.git", "web_url": "https://gitlab.qima-inc.com/fe/react-components", "readme_url": "https://gitlab.qima-inc.com/fe/react-components/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 23, "last_activity_at": "2025-07-11T06:27:31.161Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 11992, "description": "导购B端pc", "name": "guide-b-pc", "name_with_namespace": "fe / guide-b-pc", "path": "guide-b-pc", "path_with_namespace": "fe/guide-b-pc", "created_at": "2021-04-26T13:29:38.263Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/guide-b-pc.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/guide-b-pc.git", "web_url": "https://gitlab.qima-inc.com/fe/guide-b-pc", "readme_url": "https://gitlab.qima-inc.com/fe/guide-b-pc/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 1, "last_activity_at": "2025-07-11T06:23:50.824Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 15222, "description": "从 github midscene https://github.com/web-infra-dev/midscene  分叉过来的", "name": "midscene", "name_with_namespace": "fe / midscene", "path": "midscene", "path_with_namespace": "fe/midscene", "created_at": "2025-03-06T08:06:25.682Z", "default_branch": "main", "tag_list": [], "ssh_url_to_repo": "***********************:fe/midscene.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/midscene.git", "web_url": "https://gitlab.qima-inc.com/fe/midscene", "readme_url": "https://gitlab.qima-inc.com/fe/midscene/-/blob/main/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-11T06:19:12.365Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 3780, "description": "微页面杂志老数据-新数据-captain组件的转化函数", "name": "feature-adaptor", "name_with_namespace": "fe / feature-adaptor", "path": "feature-adaptor", "path_with_namespace": "fe/feature-adaptor", "created_at": "2017-11-11T09:05:08.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/feature-adaptor.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/feature-adaptor.git", "web_url": "https://gitlab.qima-inc.com/fe/feature-adaptor", "readme_url": "https://gitlab.qima-inc.com/fe/feature-adaptor/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-11T06:16:48.013Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 9989, "description": "", "name": "wsc-pc-decorate", "name_with_namespace": "wsc-node / wsc-pc-decorate", "path": "wsc-pc-decorate", "path_with_namespace": "wsc-node/wsc-pc-decorate", "created_at": "2020-07-06T09:26:49.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/wsc-pc-decorate.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-decorate.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-decorate", "readme_url": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-decorate/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 5, "last_activity_at": "2025-07-11T06:12:15.096Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 15232, "description": "", "name": "ai-data", "name_with_namespace": "fe / ai-data", "path": "ai-data", "path_with_namespace": "fe/ai-data", "created_at": "2025-03-11T11:57:03.949Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/ai-data.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/ai-data.git", "web_url": "https://gitlab.qima-inc.com/fe/ai-data", "readme_url": "https://gitlab.qima-inc.com/fe/ai-data/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-11T06:09:16.489Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 14209, "description": "ranta 兼容老定制", "name": "ranta-yun-adapter", "name_with_namespace": "fe-middle-platform / ranta-yun-adapter", "path": "ranta-yun-adapter", "path_with_namespace": "fe-middle-platform/ranta-yun-adapter", "created_at": "2022-05-27T03:33:41.661Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe-middle-platform/ranta-yun-adapter.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe-middle-platform/ranta-yun-adapter.git", "web_url": "https://gitlab.qima-inc.com/fe-middle-platform/ranta-yun-adapter", "readme_url": "https://gitlab.qima-inc.com/fe-middle-platform/ranta-yun-adapter/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-11T04:06:41.784Z", "namespace": {"id": 2081, "name": "fe-middle-platform", "path": "fe-middle-platform", "kind": "group", "full_path": "fe-middle-platform", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/2081/images.png", "web_url": "https://gitlab.qima-inc.com/groups/fe-middle-platform"}}, {"id": 14891, "description": "加我智能前端放在微商城内的页面", "name": "wsc-pc-jiawo", "name_with_namespace": "zanai / wsc-pc-jiawo", "path": "wsc-pc-jiawo", "path_with_namespace": "zanai/wsc-pc-jiawo", "created_at": "2024-01-22T02:26:06.877Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:zanai/wsc-pc-jiawo.git", "http_url_to_repo": "https://gitlab.qima-inc.com/zanai/wsc-pc-jiawo.git", "web_url": "https://gitlab.qima-inc.com/zanai/wsc-pc-jiawo", "readme_url": "https://gitlab.qima-inc.com/zanai/wsc-pc-jiawo/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-11T03:48:55.878Z", "namespace": {"id": 3740, "name": "zanai", "path": "zanai", "kind": "group", "full_path": "zanai", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/zanai"}}, {"id": 4952, "description": "Iron PC 拆分业务：数据", "name": "wsc-pc-statcenter", "name_with_namespace": "wsc-node / wsc-pc-statcenter", "path": "wsc-pc-statcenter", "path_with_namespace": "wsc-node/wsc-pc-statcenter", "created_at": "2018-06-11T03:26:10.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/wsc-pc-statcenter.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-statcenter.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-statcenter", "readme_url": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-statcenter/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-11T03:45:08.722Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 14974, "description": "", "name": "garden-echo", "name_with_namespace": "fe / garden-echo", "path": "garden-echo", "path_with_namespace": "fe/garden-echo", "created_at": "2024-05-22T09:52:32.756Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/garden-echo.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/garden-echo.git", "web_url": "https://gitlab.qima-inc.com/fe/garden-echo", "readme_url": "https://gitlab.qima-inc.com/fe/garden-echo/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-11T03:43:39.964Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 15401, "description": "中台化 H5 商家端", "name": "admin-tee-h5-app", "name_with_namespace": "fe / admin-tee-h5-app", "path": "admin-tee-h5-app", "path_with_namespace": "fe/admin-tee-h5-app", "created_at": "2025-06-12T07:15:43.446Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/admin-tee-h5-app.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/admin-tee-h5-app.git", "web_url": "https://gitlab.qima-inc.com/fe/admin-tee-h5-app", "readme_url": "https://gitlab.qima-inc.com/fe/admin-tee-h5-app/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-11T02:50:27.063Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 10757, "description": "企业微信助手B端PC应用部署仓库", "name": "weass-b-pc-dist", "name_with_namespace": "fe / weass-b-pc-dist", "path": "weass-b-pc-dist", "path_with_namespace": "fe/weass-b-pc-dist", "created_at": "2020-10-23T11:04:37.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/weass-b-pc-dist.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/weass-b-pc-dist.git", "web_url": "https://gitlab.qima-inc.com/fe/weass-b-pc-dist", "readme_url": null, "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-11T02:47:08.375Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 8648, "description": "分销员，云分销 商家者端业务", "name": "wsc-pc-salesman", "name_with_namespace": "wsc-node / wsc-pc-salesman", "path": "wsc-pc-salesman", "path_with_namespace": "wsc-node/wsc-pc-salesman", "created_at": "2019-11-28T01:21:32.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/wsc-pc-salesman.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-salesman.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-salesman", "readme_url": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-salesman/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 5, "last_activity_at": "2025-07-11T02:38:23.024Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 14318, "description": "", "name": "wsc-tee-goods-common", "name_with_namespace": "fe / wsc-tee-goods-common", "path": "wsc-goods-common", "path_with_namespace": "fe/wsc-goods-common", "created_at": "2022-08-16T02:38:25.500Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/wsc-goods-common.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/wsc-goods-common.git", "web_url": "https://gitlab.qima-inc.com/fe/wsc-goods-common", "readme_url": "https://gitlab.qima-inc.com/fe/wsc-goods-common/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-11T02:13:21.262Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 4946, "description": "Iron PC 基础业务框架", "name": "wsc-pc-base", "name_with_namespace": "wsc-node / wsc-pc-base", "path": "wsc-pc-base", "path_with_namespace": "wsc-node/wsc-pc-base", "created_at": "2018-06-11T03:03:49.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/wsc-pc-base.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-base.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-base", "readme_url": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-base/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 7, "last_activity_at": "2025-07-11T00:20:02.191Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 12783, "description": "ungoro 项目的部署仓库", "name": "ungoro-dist", "name_with_namespace": "fe / ungoro-dist", "path": "ungoro-dist", "path_with_namespace": "fe/ungoro-dist", "created_at": "2021-09-07T11:17:03.847Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/ungoro-dist.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/ungoro-dist.git", "web_url": "https://gitlab.qima-inc.com/fe/ungoro-dist", "readme_url": null, "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-11T00:10:06.041Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 12680, "description": "进店服务", "name": "Enter Shop Service", "name_with_namespace": "fe / Enter Shop Service", "path": "enter-shop-service", "path_with_namespace": "fe/enter-shop-service", "created_at": "2021-08-23T07:49:35.270Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/enter-shop-service.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/enter-shop-service.git", "web_url": "https://gitlab.qima-inc.com/fe/enter-shop-service", "readme_url": "https://gitlab.qima-inc.com/fe/enter-shop-service/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-10T11:33:39.028Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 7388, "description": "Iron H5 拆分业务：商品", "name": "wsc-h5-goods", "name_with_namespace": "wsc-node / wsc-h5-goods", "path": "wsc-h5-goods", "path_with_namespace": "wsc-node/wsc-h5-goods", "created_at": "2019-05-23T06:54:33.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/wsc-h5-goods.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-goods.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-goods", "readme_url": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-goods/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 4, "last_activity_at": "2025-07-10T11:18:05.942Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 11586, "description": "Ranta 编译时插件", "name": "ranta-compiler", "name_with_namespace": "fe-middle-platform / ranta-compiler", "path": "ranta-compiler", "path_with_namespace": "fe-middle-platform/ranta-compiler", "created_at": "2021-03-02T09:02:43.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe-middle-platform/ranta-compiler.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe-middle-platform/ranta-compiler.git", "web_url": "https://gitlab.qima-inc.com/fe-middle-platform/ranta-compiler", "readme_url": "https://gitlab.qima-inc.com/fe-middle-platform/ranta-compiler/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 1, "last_activity_at": "2025-07-10T09:17:18.137Z", "namespace": {"id": 2081, "name": "fe-middle-platform", "path": "fe-middle-platform", "kind": "group", "full_path": "fe-middle-platform", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/2081/images.png", "web_url": "https://gitlab.qima-inc.com/groups/fe-middle-platform"}}, {"id": 12139, "description": "零售跨端通用仓库", "name": "retail-tee-common", "name_with_namespace": "retail-web / retail-tee / retail-tee-common", "path": "retail-tee-common", "path_with_namespace": "retail-web/retail-tee/retail-tee-common", "created_at": "2021-05-24T09:09:28.906Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:retail-web/retail-tee/retail-tee-common.git", "http_url_to_repo": "https://gitlab.qima-inc.com/retail-web/retail-tee/retail-tee-common.git", "web_url": "https://gitlab.qima-inc.com/retail-web/retail-tee/retail-tee-common", "readme_url": "https://gitlab.qima-inc.com/retail-web/retail-tee/retail-tee-common/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-10T09:13:16.245Z", "namespace": {"id": 2872, "name": "retail-tee", "path": "retail-tee", "kind": "group", "full_path": "retail-web/retail-tee", "parent_id": 579, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/retail-web/retail-tee"}}, {"id": 7523, "description": "小程序管理平台", "name": "weapp-manager", "name_with_namespace": "weapp / weapp-manager", "path": "weapp-manager", "path_with_namespace": "weapp/weapp-manager", "created_at": "2019-06-11T13:37:59.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:weapp/weapp-manager.git", "http_url_to_repo": "https://gitlab.qima-inc.com/weapp/weapp-manager.git", "web_url": "https://gitlab.qima-inc.com/weapp/weapp-manager", "readme_url": "https://gitlab.qima-inc.com/weapp/weapp-manager/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 1, "last_activity_at": "2025-07-10T08:41:04.150Z", "namespace": {"id": 356, "name": "weapp", "path": "weapp", "kind": "group", "full_path": "weapp", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg", "web_url": "https://gitlab.qima-inc.com/groups/weapp"}}, {"id": 4951, "description": "Iron PC 拆分业务：营销", "name": "wsc-pc-ump", "name_with_namespace": "wsc-node / wsc-pc-ump", "path": "wsc-pc-ump", "path_with_namespace": "wsc-node/wsc-pc-ump", "created_at": "2018-06-11T03:21:53.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/wsc-pc-ump.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-ump.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-ump", "readme_url": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-ump/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 12, "last_activity_at": "2025-07-10T08:37:00.038Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 13965, "description": "", "name": "crm-c-h5-dist", "name_with_namespace": "fe / crm-c-h5-dist", "path": "crm-c-h5-dist", "path_with_namespace": "fe/crm-c-h5-dist", "created_at": "2022-03-14T02:23:37.999Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/crm-c-h5-dist.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/crm-c-h5-dist.git", "web_url": "https://gitlab.qima-inc.com/fe/crm-c-h5-dist", "readme_url": "https://gitlab.qima-inc.com/fe/crm-c-h5-dist/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-10T08:08:23.465Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 15033, "description": "", "name": "order-domain", "name_with_namespace": "retail-web / order-domain", "path": "order-domain", "path_with_namespace": "retail-web/order-domain", "created_at": "2024-08-13T11:13:51.445Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:retail-web/order-domain.git", "http_url_to_repo": "https://gitlab.qima-inc.com/retail-web/order-domain.git", "web_url": "https://gitlab.qima-inc.com/retail-web/order-domain", "readme_url": "https://gitlab.qima-inc.com/retail-web/order-domain/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-10T07:16:27.911Z", "namespace": {"id": 579, "name": "retail-web", "path": "retail-web", "kind": "group", "full_path": "retail-web", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/579/box-packing-peanut-512-min.png", "web_url": "https://gitlab.qima-inc.com/groups/retail-web"}}, {"id": 10635, "description": "<PERSON><PERSON> 大数据子仓库", "name": "wsc-tee-statcenter", "name_with_namespace": "weapp / wsc-tee-statcenter", "path": "wsc-tee-statcenter", "path_with_namespace": "weapp/wsc-tee-statcenter", "created_at": "2020-09-27T07:43:51.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:weapp/wsc-tee-statcenter.git", "http_url_to_repo": "https://gitlab.qima-inc.com/weapp/wsc-tee-statcenter.git", "web_url": "https://gitlab.qima-inc.com/weapp/wsc-tee-statcenter", "readme_url": "https://gitlab.qima-inc.com/weapp/wsc-tee-statcenter/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-10T06:59:28.701Z", "namespace": {"id": 356, "name": "weapp", "path": "weapp", "kind": "group", "full_path": "weapp", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg", "web_url": "https://gitlab.qima-inc.com/groups/weapp"}}, {"id": 5870, "description": "", "name": "pc-shared-service", "name_with_namespace": "wsc-node / pc-shared-service", "path": "pc-shared-service", "path_with_namespace": "wsc-node/pc-shared-service", "created_at": "2018-10-22T08:44:14.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/pc-shared-service.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/pc-shared-service.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/pc-shared-service", "readme_url": "https://gitlab.qima-inc.com/wsc-node/pc-shared-service/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 1, "last_activity_at": "2025-07-10T06:46:15.853Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 6372, "description": "pc-shared-service源码目录", "name": "pc-shared-service-source", "name_with_namespace": "fe / pc-shared-service-source", "path": "pc-shared-service-source", "path_with_namespace": "fe/pc-shared-service-source", "created_at": "2018-12-26T05:58:06.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/pc-shared-service-source.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/pc-shared-service-source.git", "web_url": "https://gitlab.qima-inc.com/fe/pc-shared-service-source", "readme_url": "https://gitlab.qima-inc.com/fe/pc-shared-service-source/-/blob/master/README.md", "avatar_url": null, "forks_count": 1, "star_count": 9, "last_activity_at": "2025-07-10T06:38:13.035Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 11447, "description": "素材管理", "name": "wsc-materials", "name_with_namespace": "wsc-node / wsc-materials", "path": "wsc-materials", "path_with_namespace": "wsc-node/wsc-materials", "created_at": "2021-02-03T03:44:53.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/wsc-materials.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/wsc-materials.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/wsc-materials", "readme_url": "https://gitlab.qima-inc.com/wsc-node/wsc-materials/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 2, "last_activity_at": "2025-07-10T02:44:50.407Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 14812, "description": "", "name": "ext-tee-user", "name_with_namespace": "weapp / ext-tee-user", "path": "ext-tee-user", "path_with_namespace": "weapp/ext-tee-user", "created_at": "2023-09-22T09:53:56.573Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:weapp/ext-tee-user.git", "http_url_to_repo": "https://gitlab.qima-inc.com/weapp/ext-tee-user.git", "web_url": "https://gitlab.qima-inc.com/weapp/ext-tee-user", "readme_url": "https://gitlab.qima-inc.com/weapp/ext-tee-user/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-10T02:11:34.029Z", "namespace": {"id": 356, "name": "weapp", "path": "weapp", "kind": "group", "full_path": "weapp", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg", "web_url": "https://gitlab.qima-inc.com/groups/weapp"}}, {"id": 11543, "description": "基于zent的PC业务通用交互模式", "name": "zent-pattern", "name_with_namespace": "fe / zent-pattern", "path": "zent-pattern", "path_with_namespace": "fe/zent-pattern", "created_at": "2021-02-23T02:13:49.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/zent-pattern.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/zent-pattern.git", "web_url": "https://gitlab.qima-inc.com/fe/zent-pattern", "readme_url": "https://gitlab.qima-inc.com/fe/zent-pattern/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-09T09:28:49.135Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 4592, "description": "Iron H5 拆分业务：各种杂七杂八业务", "name": "wsc-h5-v3", "name_with_namespace": "wsc-node / wsc-h5-v3", "path": "wsc-h5-v3", "path_with_namespace": "wsc-node/wsc-h5-v3", "created_at": "2018-04-19T03:26:13.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/wsc-h5-v3.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-v3.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-v3", "readme_url": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-v3/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 1, "last_activity_at": "2025-07-09T07:54:46.046Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 9617, "description": "三方渠道", "name": "wsc-pc-channel", "name_with_namespace": "wsc-node / wsc-pc-channel", "path": "wsc-pc-channel", "path_with_namespace": "wsc-node/wsc-pc-channel", "created_at": "2020-05-18T02:53:27.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/wsc-pc-channel.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-channel.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-channel", "readme_url": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-channel/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-09T07:38:36.417Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 4948, "description": "Iron PC 拆分业务：杂七杂八业务，设置", "name": "wsc-pc-v4", "name_with_namespace": "wsc-node / wsc-pc-v4", "path": "wsc-pc-v4", "path_with_namespace": "wsc-node/wsc-pc-v4", "created_at": "2018-06-11T03:10:04.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/wsc-pc-v4.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-v4.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-v4", "readme_url": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-v4/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 7, "last_activity_at": "2025-07-09T07:19:06.577Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 12947, "description": "", "name": "salesman-mono", "name_with_namespace": "fe / salesman-mono", "path": "salesman-mono", "path_with_namespace": "fe/salesman-mono", "created_at": "2021-10-08T11:54:41.260Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/salesman-mono.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/salesman-mono.git", "web_url": "https://gitlab.qima-inc.com/fe/salesman-mono", "readme_url": "https://gitlab.qima-inc.com/fe/salesman-mono/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 1, "last_activity_at": "2025-07-09T07:05:20.843Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 11814, "description": "", "name": "wsc-tee-salesman", "name_with_namespace": "weapp / wsc-tee-salesman", "path": "wsc-tee-salesman", "path_with_namespace": "weapp/wsc-tee-salesman", "created_at": "2021-04-01T09:41:17.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:weapp/wsc-tee-salesman.git", "http_url_to_repo": "https://gitlab.qima-inc.com/weapp/wsc-tee-salesman.git", "web_url": "https://gitlab.qima-inc.com/weapp/wsc-tee-salesman", "readme_url": "https://gitlab.qima-inc.com/weapp/wsc-tee-salesman/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 2, "last_activity_at": "2025-07-09T07:05:07.522Z", "namespace": {"id": 356, "name": "weapp", "path": "weapp", "kind": "group", "full_path": "weapp", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg", "web_url": "https://gitlab.qima-inc.com/groups/weapp"}}, {"id": 5902, "description": "App 应用市场 app.youzanyun.com", "name": "app-web", "name_with_namespace": "fe-ecloud / app-web", "path": "app-web", "path_with_namespace": "fe-ecloud/app-web", "created_at": "2018-10-29T07:38:04.000Z", "tag_list": [], "ssh_url_to_repo": "***********************:fe-ecloud/app-web.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe-ecloud/app-web.git", "web_url": "https://gitlab.qima-inc.com/fe-ecloud/app-web", "readme_url": "https://gitlab.qima-inc.com/fe-ecloud/app-web/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 3, "last_activity_at": "2025-07-09T06:14:40.171Z", "namespace": {"id": 1334, "name": "fe-ecloud", "path": "fe-ecloud", "kind": "group", "full_path": "fe-ecloud", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/fe-ecloud"}}, {"id": 4700, "description": "The distribution of im-node.", "name": "im-node-dist", "name_with_namespace": "fe / im-node-dist", "path": "im-node-dist", "path_with_namespace": "fe/im-node-dist", "created_at": "2018-05-09T03:03:36.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/im-node-dist.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/im-node-dist.git", "web_url": "https://gitlab.qima-inc.com/fe/im-node-dist", "readme_url": null, "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-09T06:11:30.514Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 6999, "description": "C端IM发布仓库", "name": "wap-im-dist", "name_with_namespace": "fe / wap-im-dist", "path": "wap-im-dist", "path_with_namespace": "fe/wap-im-dist", "created_at": "2019-04-03T02:04:26.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/wap-im-dist.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/wap-im-dist.git", "web_url": "https://gitlab.qima-inc.com/fe/wap-im-dist", "readme_url": "https://gitlab.qima-inc.com/fe/wap-im-dist/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-09T03:59:02.419Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 6998, "description": "C端IM源码仓库", "name": "wap-im-web", "name_with_namespace": "fe / wap-im-web", "path": "wap-im-web", "path_with_namespace": "fe/wap-im-web", "created_at": "2019-04-03T02:03:49.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/wap-im-web.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/wap-im-web.git", "web_url": "https://gitlab.qima-inc.com/fe/wap-im-web", "readme_url": "https://gitlab.qima-inc.com/fe/wap-im-web/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 1, "last_activity_at": "2025-07-09T03:55:53.901Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 14634, "description": "", "name": "cloud-ranta-tee", "name_with_namespace": "fe-middle-platform / cloud-ranta-tee", "path": "cloud-ranta-tee", "path_with_namespace": "fe-middle-platform/cloud-ranta-tee", "created_at": "2023-05-08T02:48:30.979Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe-middle-platform/cloud-ranta-tee.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe-middle-platform/cloud-ranta-tee.git", "web_url": "https://gitlab.qima-inc.com/fe-middle-platform/cloud-ranta-tee", "readme_url": "https://gitlab.qima-inc.com/fe-middle-platform/cloud-ranta-tee/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-09T03:43:21.151Z", "namespace": {"id": 2081, "name": "fe-middle-platform", "path": "fe-middle-platform", "kind": "group", "full_path": "fe-middle-platform", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/2081/images.png", "web_url": "https://gitlab.qima-inc.com/groups/fe-middle-platform"}}, {"id": 12767, "description": "CRM 租户标处理", "name": "platform-tenant", "name_with_namespace": "fe / platform-tenant", "path": "platform-tenant", "path_with_namespace": "fe/platform-tenant", "created_at": "2021-09-06T03:47:34.991Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/platform-tenant.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/platform-tenant.git", "web_url": "https://gitlab.qima-inc.com/fe/platform-tenant", "readme_url": "https://gitlab.qima-inc.com/fe/platform-tenant/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-09T03:31:35.799Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 10934, "description": "有赞 scrm 店铺升级检查插件", "name": "scrm-shop-check-plugin", "name_with_namespace": "fe / scrm-shop-check-plugin", "path": "scrm-shop-check-plugin", "path_with_namespace": "fe/scrm-shop-check-plugin", "created_at": "2020-11-20T04:07:04.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/scrm-shop-check-plugin.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/scrm-shop-check-plugin.git", "web_url": "https://gitlab.qima-inc.com/fe/scrm-shop-check-plugin", "readme_url": "https://gitlab.qima-inc.com/fe/scrm-shop-check-plugin/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-09T03:29:06.187Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 6321, "description": "", "name": "trash", "name_with_namespace": "chenting / trash", "path": "trash", "path_with_namespace": "chenting/trash", "created_at": "2018-12-18T18:23:00.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:chenting/trash.git", "http_url_to_repo": "https://gitlab.qima-inc.com/chenting/trash.git", "web_url": "https://gitlab.qima-inc.com/chenting/trash", "readme_url": "https://gitlab.qima-inc.com/chenting/trash/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-09T02:46:33.795Z", "namespace": {"id": 904, "name": "chenting", "path": "chenting", "kind": "user", "full_path": "chenting", "parent_id": null, "avatar_url": "/uploads/-/system/user/avatar/807/avatar.png", "web_url": "https://gitlab.qima-inc.com/chenting"}}, {"id": 9467, "description": "", "name": "wsc-pc-apps", "name_with_namespace": "fe-ecloud / wsc-pc-apps", "path": "wsc-pc-apps", "path_with_namespace": "fe-ecloud/wsc-pc-apps", "created_at": "2020-04-26T11:29:29.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe-ecloud/wsc-pc-apps.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe-ecloud/wsc-pc-apps.git", "web_url": "https://gitlab.qima-inc.com/fe-ecloud/wsc-pc-apps", "readme_url": "https://gitlab.qima-inc.com/fe-ecloud/wsc-pc-apps/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 2, "last_activity_at": "2025-07-09T02:20:03.889Z", "namespace": {"id": 1334, "name": "fe-ecloud", "path": "fe-ecloud", "kind": "group", "full_path": "fe-ecloud", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/fe-ecloud"}}, {"id": 14975, "description": "", "name": "echo-manage", "name_with_namespace": "fe / echo-manage", "path": "echo-manage", "path_with_namespace": "fe/echo-manage", "created_at": "2024-05-23T02:31:29.417Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/echo-manage.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/echo-manage.git", "web_url": "https://gitlab.qima-inc.com/fe/echo-manage", "readme_url": "https://gitlab.qima-inc.com/fe/echo-manage/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 1, "last_activity_at": "2025-07-09T02:14:59.731Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 7832, "description": "Ranta 沙箱", "name": "ranta-sandbox", "name_with_namespace": "fe-middle-platform / ranta-sandbox", "path": "ranta-sandbox", "path_with_namespace": "fe-middle-platform/ranta-sandbox", "created_at": "2019-07-24T07:45:37.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe-middle-platform/ranta-sandbox.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe-middle-platform/ranta-sandbox.git", "web_url": "https://gitlab.qima-inc.com/fe-middle-platform/ranta-sandbox", "readme_url": "https://gitlab.qima-inc.com/fe-middle-platform/ranta-sandbox/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 4, "last_activity_at": "2025-07-08T12:03:21.241Z", "namespace": {"id": 2081, "name": "fe-middle-platform", "path": "fe-middle-platform", "kind": "group", "full_path": "fe-middle-platform", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/2081/images.png", "web_url": "https://gitlab.qima-inc.com/groups/fe-middle-platform"}}, {"id": 11220, "description": "企助海报服务部署仓库", "name": "weass-snapshot-dist", "name_with_namespace": "fe / weass-snapshot-dist", "path": "weass-snapshot-dist", "path_with_namespace": "fe/weass-snapshot-dist", "created_at": "2021-01-04T09:57:23.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/weass-snapshot-dist.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/weass-snapshot-dist.git", "web_url": "https://gitlab.qima-inc.com/fe/weass-snapshot-dist", "readme_url": null, "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-08T12:02:07.670Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 10756, "description": "企业微信助手C端 monorepo", "name": "weass-c-mono", "name_with_namespace": "fe / weass-c-mono", "path": "weass-c-mono", "path_with_namespace": "fe/weass-c-mono", "created_at": "2020-10-23T11:02:13.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/weass-c-mono.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/weass-c-mono.git", "web_url": "https://gitlab.qima-inc.com/fe/weass-c-mono", "readme_url": "https://gitlab.qima-inc.com/fe/weass-c-mono/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-08T11:59:35.331Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 10758, "description": "企业微信助手B端H5应用部署仓库", "name": "weass-b-h5-dist", "name_with_namespace": "fe / weass-b-h5-dist", "path": "weass-b-h5-dist", "path_with_namespace": "fe/weass-b-h5-dist", "created_at": "2020-10-23T11:13:14.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/weass-b-h5-dist.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/weass-b-h5-dist.git", "web_url": "https://gitlab.qima-inc.com/fe/weass-b-h5-dist", "readme_url": null, "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-08T07:15:59.143Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 10967, "description": "", "name": "garden", "name_with_namespace": "fe / garden", "path": "garden", "path_with_namespace": "fe/garden", "created_at": "2020-11-26T03:03:50.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/garden.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/garden.git", "web_url": "https://gitlab.qima-inc.com/fe/garden", "readme_url": "https://gitlab.qima-inc.com/fe/garden/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 3, "last_activity_at": "2025-07-08T07:15:34.331Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 15027, "description": "", "name": "garden-ipaas", "name_with_namespace": "fe / garden-ipaas", "path": "garden-ipaas", "path_with_namespace": "fe/garden-ipaas", "created_at": "2024-08-07T08:12:42.138Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/garden-ipaas.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/garden-ipaas.git", "web_url": "https://gitlab.qima-inc.com/fe/garden-ipaas", "readme_url": "https://gitlab.qima-inc.com/fe/garden-ipaas/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-08T06:59:56.578Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 12279, "description": "", "name": "goods-domain", "name_with_namespace": "fe / goods-domain", "path": "goods-domain", "path_with_namespace": "fe/goods-domain", "created_at": "2021-06-17T11:40:12.044Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/goods-domain.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/goods-domain.git", "web_url": "https://gitlab.qima-inc.com/fe/goods-domain", "readme_url": "https://gitlab.qima-inc.com/fe/goods-domain/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 1, "last_activity_at": "2025-07-08T06:31:14.943Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 12519, "description": "", "name": "Taurus Front", "name_with_namespace": "fe / Taurus Front", "path": "taurus-front", "path_with_namespace": "fe/taurus-front", "created_at": "2021-07-26T06:09:01.402Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/taurus-front.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/taurus-front.git", "web_url": "https://gitlab.qima-inc.com/fe/taurus-front", "readme_url": "https://gitlab.qima-inc.com/fe/taurus-front/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-08T03:24:15.309Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 9985, "description": "", "name": "wsc-h5-statcenter", "name_with_namespace": "wsc-node / wsc-h5-statcenter", "path": "wsc-h5-statcenter", "path_with_namespace": "wsc-node/wsc-h5-statcenter", "created_at": "2020-07-06T07:29:26.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/wsc-h5-statcenter.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-statcenter.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-statcenter", "readme_url": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-statcenter/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-08T02:18:12.111Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 12843, "description": "", "name": "garden-gdp", "name_with_namespace": "fe / garden-gdp", "path": "garden-gdp", "path_with_namespace": "fe/garden-gdp", "created_at": "2021-09-16T13:32:45.469Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/garden-gdp.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/garden-gdp.git", "web_url": "https://gitlab.qima-inc.com/fe/garden-gdp", "readme_url": "https://gitlab.qima-inc.com/fe/garden-gdp/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-07T12:25:38.488Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 15402, "description": "", "name": "test", "name_with_namespace": "xujiazheng / test", "path": "test", "path_with_namespace": "xujiazheng/test", "created_at": "2025-06-13T03:23:21.547Z", "default_branch": "main", "tag_list": [], "ssh_url_to_repo": "***********************:xujiazheng/test.git", "http_url_to_repo": "https://gitlab.qima-inc.com/xujiazheng/test.git", "web_url": "https://gitlab.qima-inc.com/xujiazheng/test", "readme_url": null, "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-07T11:54:48.693Z", "namespace": {"id": 3020, "name": "xu<PERSON>azheng", "path": "xu<PERSON>azheng", "kind": "user", "full_path": "xu<PERSON>azheng", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/xujiazheng"}}, {"id": 11535, "description": "", "name": "ui-test", "name_with_namespace": "retail-web / ui-test", "path": "ui-test", "path_with_namespace": "retail-web/ui-test", "created_at": "2021-02-22T02:30:40.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:retail-web/ui-test.git", "http_url_to_repo": "https://gitlab.qima-inc.com/retail-web/ui-test.git", "web_url": "https://gitlab.qima-inc.com/retail-web/ui-test", "readme_url": "https://gitlab.qima-inc.com/retail-web/ui-test/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 1, "last_activity_at": "2025-07-07T09:35:40.562Z", "namespace": {"id": 579, "name": "retail-web", "path": "retail-web", "kind": "group", "full_path": "retail-web", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/579/box-packing-peanut-512-min.png", "web_url": "https://gitlab.qima-inc.com/groups/retail-web"}}, {"id": 14833, "description": "新零售运营平台", "name": "garden-retail", "name_with_namespace": "fe / garden-retail", "path": "garden-retail", "path_with_namespace": "fe/garden-retail", "created_at": "2023-10-25T03:19:09.089Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/garden-retail.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/garden-retail.git", "web_url": "https://gitlab.qima-inc.com/fe/garden-retail", "readme_url": "https://gitlab.qima-inc.com/fe/garden-retail/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-07T07:15:22.540Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 11661, "description": "账号 - 多端中台化扩展仓库", "name": "ext-tee-passport", "name_with_namespace": "weapp / ext-tee-passport", "path": "ext-tee-passport", "path_with_namespace": "weapp/ext-tee-passport", "created_at": "2021-03-12T09:37:42.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:weapp/ext-tee-passport.git", "http_url_to_repo": "https://gitlab.qima-inc.com/weapp/ext-tee-passport.git", "web_url": "https://gitlab.qima-inc.com/weapp/ext-tee-passport", "readme_url": "https://gitlab.qima-inc.com/weapp/ext-tee-passport/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 1, "last_activity_at": "2025-07-07T03:46:51.226Z", "namespace": {"id": 356, "name": "weapp", "path": "weapp", "kind": "group", "full_path": "weapp", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg", "web_url": "https://gitlab.qima-inc.com/groups/weapp"}}, {"id": 15323, "description": "", "name": "<PERSON><PERSON>-assistant", "name_with_namespace": "fe / jira-assistant", "path": "<PERSON><PERSON>-assistant", "path_with_namespace": "fe/jira-assistant", "created_at": "2025-05-19T06:24:52.694Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/jira-assistant.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/jira-assistant.git", "web_url": "https://gitlab.qima-inc.com/fe/jira-assistant", "readme_url": "https://gitlab.qima-inc.com/fe/jira-assistant/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-07T02:53:21.086Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 10969, "description": "", "name": "wsc-h5-aftersales", "name_with_namespace": "wsc-node / wsc-h5-aftersales", "path": "wsc-h5-aftersales", "path_with_namespace": "wsc-node/wsc-h5-aftersales", "created_at": "2020-11-26T05:33:00.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/wsc-h5-aftersales.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-aftersales.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-aftersales", "readme_url": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-aftersales/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 3, "last_activity_at": "2025-07-06T16:00:08.908Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 12261, "description": "微信视频号", "name": "wsc-pc-wxvideo", "name_with_namespace": "wsc-node / wsc-pc-wxvideo", "path": "wsc-pc-wxvideo", "path_with_namespace": "wsc-node/wsc-pc-wxvideo", "created_at": "2021-06-16T03:23:50.794Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/wsc-pc-wxvideo.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-wxvideo.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-wxvideo", "readme_url": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-wxvideo/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-04T16:10:36.531Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 4422, "description": "会员中心，权益卡等会员 C 端业务", "name": "wsc-h5-user", "name_with_namespace": "wsc-node / wsc-h5-user", "path": "wsc-h5-user", "path_with_namespace": "wsc-node/wsc-h5-user", "created_at": "2018-03-22T03:07:08.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/wsc-h5-user.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-user.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-user", "readme_url": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-user/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 5, "last_activity_at": "2025-07-04T16:10:30.802Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 11626, "description": "", "name": "matrix-manage-node-dist", "name_with_namespace": "fe / matrix-manage-node-dist", "path": "matrix-manage-node-dist", "path_with_namespace": "fe/matrix-manage-node-dist", "created_at": "2021-03-08T12:18:27.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/matrix-manage-node-dist.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/matrix-manage-node-dist.git", "web_url": "https://gitlab.qima-inc.com/fe/matrix-manage-node-dist", "readme_url": "https://gitlab.qima-inc.com/fe/matrix-manage-node-dist/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-04T16:10:29.390Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 11625, "description": "", "name": "matrix-runtime", "name_with_namespace": "fe / matrix-runtime", "path": "matrix-runtime", "path_with_namespace": "fe/matrix-runtime", "created_at": "2021-03-08T12:16:43.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/matrix-runtime.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/matrix-runtime.git", "web_url": "https://gitlab.qima-inc.com/fe/matrix-runtime", "readme_url": null, "avatar_url": null, "forks_count": 0, "star_count": 1, "last_activity_at": "2025-07-04T16:10:28.781Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 11617, "description": "产品矩阵管理后台", "name": "matrix-manage", "name_with_namespace": "fe / matrix-manage", "path": "matrix-manage", "path_with_namespace": "fe/matrix-manage", "created_at": "2021-03-08T03:22:55.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/matrix-manage.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/matrix-manage.git", "web_url": "https://gitlab.qima-inc.com/fe/matrix-manage", "readme_url": null, "avatar_url": null, "forks_count": 0, "star_count": 2, "last_activity_at": "2025-07-04T16:10:28.346Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 11302, "description": "CDP(custom data platform) front end project", "name": "cdp-front", "name_with_namespace": "fe / cdp-front", "path": "cdp-front", "path_with_namespace": "fe/cdp-front", "created_at": "2021-01-12T01:51:53.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/cdp-front.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/cdp-front.git", "web_url": "https://gitlab.qima-inc.com/fe/cdp-front", "readme_url": "https://gitlab.qima-inc.com/fe/cdp-front/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 4, "last_activity_at": "2025-07-04T16:10:20.140Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 14137, "description": "", "name": "fe-service-mono", "name_with_namespace": "fe / fe-service-mono", "path": "fe-service-mono", "path_with_namespace": "fe/fe-service-mono", "created_at": "2022-04-25T02:27:58.289Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/fe-service-mono.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/fe-service-mono.git", "web_url": "https://gitlab.qima-inc.com/fe/fe-service-mono", "readme_url": "https://gitlab.qima-inc.com/fe/fe-service-mono/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-04T16:10:18.720Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 11952, "description": "企微助手交付自动化应用部署仓库", "name": "weass-automation-dist", "name_with_namespace": "fe / weass-automation-dist", "path": "weass-automation-dist", "path_with_namespace": "fe/weass-automation-dist", "created_at": "2021-04-22T06:33:23.147Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/weass-automation-dist.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/weass-automation-dist.git", "web_url": "https://gitlab.qima-inc.com/fe/weass-automation-dist", "readme_url": null, "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-04T16:10:18.226Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 13058, "description": "企微助手服务商自动化 Node 应用", "name": "weass-service-provider-automation-dist", "name_with_namespace": "fe / weass-service-provider-automation-dist", "path": "weass-service-provider-automation-dist", "path_with_namespace": "fe/weass-service-provider-automation-dist", "created_at": "2021-10-26T07:54:26.228Z", "default_branch": "feat/service-automation-configuration", "tag_list": [], "ssh_url_to_repo": "***********************:fe/weass-service-provider-automation-dist.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/weass-service-provider-automation-dist.git", "web_url": "https://gitlab.qima-inc.com/fe/weass-service-provider-automation-dist", "readme_url": null, "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-04T16:10:17.763Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 7703, "description": "", "name": "wsc-fe-pc-decorate-components", "name_with_namespace": "wsc-node / wsc-fe-pc-decorate-components", "path": "wsc-fe-pc-decorate-components", "path_with_namespace": "wsc-node/wsc-fe-pc-decorate-components", "created_at": "2019-07-09T02:26:01.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/wsc-fe-pc-decorate-components.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/wsc-fe-pc-decorate-components.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/wsc-fe-pc-decorate-components", "readme_url": "https://gitlab.qima-inc.com/wsc-node/wsc-fe-pc-decorate-components/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 2, "last_activity_at": "2025-07-04T06:57:19.281Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 14843, "description": "", "name": "cloud-api-types", "name_with_namespace": "fe-middle-platform / cloud-api-types", "path": "cloud-api-types", "path_with_namespace": "fe-middle-platform/cloud-api-types", "created_at": "2023-11-07T02:45:26.728Z", "default_branch": "main", "tag_list": [], "ssh_url_to_repo": "***********************:fe-middle-platform/cloud-api-types.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe-middle-platform/cloud-api-types.git", "web_url": "https://gitlab.qima-inc.com/fe-middle-platform/cloud-api-types", "readme_url": null, "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-04T06:24:10.877Z", "namespace": {"id": 2081, "name": "fe-middle-platform", "path": "fe-middle-platform", "kind": "group", "full_path": "fe-middle-platform", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/2081/images.png", "web_url": "https://gitlab.qima-inc.com/groups/fe-middle-platform"}}, {"id": 11966, "description": "Awesome React Hooks ", "name": "react-hooks", "name_with_namespace": "fe / react-hooks", "path": "react-hooks", "path_with_namespace": "fe/react-hooks", "created_at": "2021-04-23T08:32:22.015Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/react-hooks.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/react-hooks.git", "web_url": "https://gitlab.qima-inc.com/fe/react-hooks", "readme_url": "https://gitlab.qima-inc.com/fe/react-hooks/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 1, "last_activity_at": "2025-07-04T03:27:01.385Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 11887, "description": "", "name": "ump-biz-utils", "name_with_namespace": "fe / ump-biz-utils", "path": "ump-biz-utils", "path_with_namespace": "fe/ump-biz-utils", "created_at": "2021-04-16T08:34:24.845Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/ump-biz-utils.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/ump-biz-utils.git", "web_url": "https://gitlab.qima-inc.com/fe/ump-biz-utils", "readme_url": "https://gitlab.qima-inc.com/fe/ump-biz-utils/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-04T02:48:27.151Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 12826, "description": "视频号H5业务", "name": "wsc-h5-wxvideo", "name_with_namespace": "wsc-node / wsc-h5-wxvideo", "path": "wsc-h5-wxvideo", "path_with_namespace": "wsc-node/wsc-h5-wxvideo", "created_at": "2021-09-15T06:43:13.254Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/wsc-h5-wxvideo.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-wxvideo.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-wxvideo", "readme_url": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-wxvideo/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 1, "last_activity_at": "2025-07-04T02:40:36.739Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 2404, "description": "分销前端仓库，从原来的iron-front clone出来的。", "name": "fen<PERSON><PERSON>", "name_with_namespace": "fe / fenxiao", "path": "fen<PERSON><PERSON>", "path_with_namespace": "fe/fenxiao", "created_at": "2017-04-26T09:54:50.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/fenxiao.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/fenxiao.git", "web_url": "https://gitlab.qima-inc.com/fe/fenxiao", "readme_url": "https://gitlab.qima-inc.com/fe/fenxiao/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 2, "last_activity_at": "2025-07-03T12:03:03.052Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 11867, "description": "", "name": "goods-biz-utils", "name_with_namespace": "fe / goods-biz-utils", "path": "goods-biz-utils", "path_with_namespace": "fe/goods-biz-utils", "created_at": "2021-04-14T10:25:48.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/goods-biz-utils.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/goods-biz-utils.git", "web_url": "https://gitlab.qima-inc.com/fe/goods-biz-utils", "readme_url": "https://gitlab.qima-inc.com/fe/goods-biz-utils/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-03T08:29:11.894Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 12055, "description": "", "name": "Npm Platform Web Dist", "name_with_namespace": "fe / Npm Platform Web Dist", "path": "*********************", "path_with_namespace": "fe/*********************", "created_at": "2021-05-10T12:31:03.365Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/*********************.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/*********************.git", "web_url": "https://gitlab.qima-inc.com/fe/*********************", "readme_url": null, "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-03T08:15:07.059Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 10213, "description": "", "name": "wsc-tee-goods", "name_with_namespace": "weapp / wsc-tee-goods", "path": "wsc-tee-goods", "path_with_namespace": "weapp/wsc-tee-goods", "created_at": "2020-08-04T09:08:23.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:weapp/wsc-tee-goods.git", "http_url_to_repo": "https://gitlab.qima-inc.com/weapp/wsc-tee-goods.git", "web_url": "https://gitlab.qima-inc.com/weapp/wsc-tee-goods", "readme_url": "https://gitlab.qima-inc.com/weapp/wsc-tee-goods/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 2, "last_activity_at": "2025-07-03T07:48:35.688Z", "namespace": {"id": 356, "name": "weapp", "path": "weapp", "kind": "group", "full_path": "weapp", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg", "web_url": "https://gitlab.qima-inc.com/groups/weapp"}}, {"id": 11084, "description": "", "name": "wsc-tee-shop", "name_with_namespace": "weapp / wsc-tee-shop", "path": "wsc-tee-shop", "path_with_namespace": "weapp/wsc-tee-shop", "created_at": "2020-12-14T02:52:46.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:weapp/wsc-tee-shop.git", "http_url_to_repo": "https://gitlab.qima-inc.com/weapp/wsc-tee-shop.git", "web_url": "https://gitlab.qima-inc.com/weapp/wsc-tee-shop", "readme_url": "https://gitlab.qima-inc.com/weapp/wsc-tee-shop/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-03T06:55:48.705Z", "namespace": {"id": 356, "name": "weapp", "path": "weapp", "kind": "group", "full_path": "weapp", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg", "web_url": "https://gitlab.qima-inc.com/groups/weapp"}}, {"id": 15044, "description": "", "name": "ipaas", "name_with_namespace": "fe / ipaas", "path": "ipaas", "path_with_namespace": "fe/ipaas", "created_at": "2024-08-23T09:58:54.262Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/ipaas.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/ipaas.git", "web_url": "https://gitlab.qima-inc.com/fe/ipaas", "readme_url": "https://gitlab.qima-inc.com/fe/ipaas/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-03T06:05:20.720Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 10177, "description": "<PERSON><PERSON>的业务生态", "name": "tee-biz", "name_with_namespace": "fe-middle-platform / tee-biz", "path": "tee-biz", "path_with_namespace": "fe-middle-platform/tee-biz", "created_at": "2020-07-29T08:55:04.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe-middle-platform/tee-biz.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe-middle-platform/tee-biz.git", "web_url": "https://gitlab.qima-inc.com/fe-middle-platform/tee-biz", "readme_url": "https://gitlab.qima-inc.com/fe-middle-platform/tee-biz/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 2, "last_activity_at": "2025-07-03T03:34:15.702Z", "namespace": {"id": 2081, "name": "fe-middle-platform", "path": "fe-middle-platform", "kind": "group", "full_path": "fe-middle-platform", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/2081/images.png", "web_url": "https://gitlab.qima-inc.com/groups/fe-middle-platform"}}, {"id": 12835, "description": "", "name": "ungoro-mono", "name_with_namespace": "fe / ungoro-mono", "path": "ungoro-mono", "path_with_namespace": "fe/ungoro-mono", "created_at": "2021-09-15T10:41:55.415Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/ungoro-mono.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/ungoro-mono.git", "web_url": "https://gitlab.qima-inc.com/fe/ungoro-mono", "readme_url": "https://gitlab.qima-inc.com/fe/ungoro-mono/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-03T02:53:32.101Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 5377, "description": "面向商家端 —— 帐号系统", "name": "wsc-pc-account", "name_with_namespace": "wsc-node / wsc-pc-account", "path": "wsc-pc-account", "path_with_namespace": "wsc-node/wsc-pc-account", "created_at": "2018-08-10T03:08:15.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/wsc-pc-account.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-account.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-account", "readme_url": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-account/-/blob/master/README.md", "avatar_url": null, "forks_count": 1, "star_count": 1, "last_activity_at": "2025-07-02T16:00:07.466Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 10070, "description": "帐号侧统一 NPM 包集合", "name": "account-plugin", "name_with_namespace": "fe / account-plugin", "path": "account-plugin", "path_with_namespace": "fe/account-plugin", "created_at": "2020-07-15T08:52:52.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/account-plugin.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/account-plugin.git", "web_url": "https://gitlab.qima-inc.com/fe/account-plugin", "readme_url": "https://gitlab.qima-inc.com/fe/account-plugin/-/blob/master/README.md", "avatar_url": "https://gitlab.qima-inc.com/uploads/-/system/project/avatar/10070/account-128.jpg", "forks_count": 0, "star_count": 1, "last_activity_at": "2025-07-02T10:10:42.172Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 5939, "description": "面向消费者端 —— 帐号系统", "name": "wsc-h5-account", "name_with_namespace": "wsc-node / wsc-h5-account", "path": "wsc-h5-account", "path_with_namespace": "wsc-node/wsc-h5-account", "created_at": "2018-11-01T08:32:03.000Z", "default_branch": "master", "tag_list": ["H5", "账号"], "ssh_url_to_repo": "***********************:wsc-node/wsc-h5-account.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-account.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-account", "readme_url": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-account/-/blob/master/README.md", "avatar_url": "https://gitlab.qima-inc.com/uploads/-/system/project/avatar/5939/account-128.jpg", "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-02T08:14:59.011Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 15310, "description": "小红书Demo展示小程序", "name": "demoApp", "name_with_namespace": "fe / demoApp", "path": "xhsdemo", "path_with_namespace": "fe/xhsdemo", "created_at": "2025-05-07T06:11:14.776Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/xhsdemo.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/xhsdemo.git", "web_url": "https://gitlab.qima-inc.com/fe/xhsdemo", "readme_url": "https://gitlab.qima-inc.com/fe/xhsdemo/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 1, "last_activity_at": "2025-07-02T07:32:41.544Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 8177, "description": "行业电商：酒店", "name": "wsc-pc-industry", "name_with_namespace": "wsc-node / wsc-pc-industry", "path": "wsc-pc-industry", "path_with_namespace": "wsc-node/wsc-pc-industry", "created_at": "2019-09-11T08:02:59.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/wsc-pc-industry.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-industry.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-industry", "readme_url": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-industry/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-02T03:53:34.294Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 12509, "description": "", "name": "ext-tee-guide", "name_with_namespace": "weapp / ext-tee-guide", "path": "ext-tee-guide", "path_with_namespace": "weapp/ext-tee-guide", "created_at": "2021-07-22T03:45:14.504Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:weapp/ext-tee-guide.git", "http_url_to_repo": "https://gitlab.qima-inc.com/weapp/ext-tee-guide.git", "web_url": "https://gitlab.qima-inc.com/weapp/ext-tee-guide", "readme_url": "https://gitlab.qima-inc.com/weapp/ext-tee-guide/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-01T12:17:53.532Z", "namespace": {"id": 356, "name": "weapp", "path": "weapp", "kind": "group", "full_path": "weapp", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg", "web_url": "https://gitlab.qima-inc.com/groups/weapp"}}, {"id": 15325, "description": "", "name": "open-webui", "name_with_namespace": "fe / open-webui", "path": "open-webui", "path_with_namespace": "fe/open-webui", "created_at": "2025-05-20T10:10:42.115Z", "default_branch": "main", "tag_list": [], "ssh_url_to_repo": "***********************:fe/open-webui.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/open-webui.git", "web_url": "https://gitlab.qima-inc.com/fe/open-webui", "readme_url": "https://gitlab.qima-inc.com/fe/open-webui/-/blob/main/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-01T08:50:23.498Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 15257, "description": "", "name": "rpa-proxy", "name_with_namespace": "fe-middle-platform / rpa-proxy", "path": "rpa-proxy", "path_with_namespace": "fe-middle-platform/rpa-proxy", "created_at": "2025-03-25T12:01:00.285Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe-middle-platform/rpa-proxy.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe-middle-platform/rpa-proxy.git", "web_url": "https://gitlab.qima-inc.com/fe-middle-platform/rpa-proxy", "readme_url": "https://gitlab.qima-inc.com/fe-middle-platform/rpa-proxy/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-01T06:15:28.791Z", "namespace": {"id": 2081, "name": "fe-middle-platform", "path": "fe-middle-platform", "kind": "group", "full_path": "fe-middle-platform", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/2081/images.png", "web_url": "https://gitlab.qima-inc.com/groups/fe-middle-platform"}}, {"id": 11988, "description": "消息 - 多端中台化扩展仓库", "name": "ext-tee-wsc-im", "name_with_namespace": "weapp / ext-tee-wsc-im", "path": "ext-tee-wsc-im", "path_with_namespace": "weapp/ext-tee-wsc-im", "created_at": "2021-04-26T09:55:17.588Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:weapp/ext-tee-wsc-im.git", "http_url_to_repo": "https://gitlab.qima-inc.com/weapp/ext-tee-wsc-im.git", "web_url": "https://gitlab.qima-inc.com/weapp/ext-tee-wsc-im", "readme_url": "https://gitlab.qima-inc.com/weapp/ext-tee-wsc-im/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-07-01T03:52:03.907Z", "namespace": {"id": 356, "name": "weapp", "path": "weapp", "kind": "group", "full_path": "weapp", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg", "web_url": "https://gitlab.qima-inc.com/groups/weapp"}}, {"id": 10215, "description": "早期纯多端仓库(被wsc-tee-base依赖)，装修子仓库，目前仅用于QQ、支付宝小程序", "name": "wsc-tee-decorate", "name_with_namespace": "weapp / wsc-tee-decorate", "path": "wsc-tee-decorate", "path_with_namespace": "weapp/wsc-tee-decorate", "created_at": "2020-08-04T09:09:09.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:weapp/wsc-tee-decorate.git", "http_url_to_repo": "https://gitlab.qima-inc.com/weapp/wsc-tee-decorate.git", "web_url": "https://gitlab.qima-inc.com/weapp/wsc-tee-decorate", "readme_url": "https://gitlab.qima-inc.com/weapp/wsc-tee-decorate/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 2, "last_activity_at": "2025-07-01T03:14:02.554Z", "namespace": {"id": 356, "name": "weapp", "path": "weapp", "kind": "group", "full_path": "weapp", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg", "web_url": "https://gitlab.qima-inc.com/groups/weapp"}}, {"id": 9950, "description": "多端 Vant 组件库", "name": "vant-tee", "name_with_namespace": "fe-middle-platform / vant-tee", "path": "vant-tee", "path_with_namespace": "fe-middle-platform/vant-tee", "created_at": "2020-07-02T03:17:14.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe-middle-platform/vant-tee.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe-middle-platform/vant-tee.git", "web_url": "https://gitlab.qima-inc.com/fe-middle-platform/vant-tee", "readme_url": "https://gitlab.qima-inc.com/fe-middle-platform/vant-tee/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 4, "last_activity_at": "2025-06-30T12:02:46.370Z", "namespace": {"id": 2081, "name": "fe-middle-platform", "path": "fe-middle-platform", "kind": "group", "full_path": "fe-middle-platform", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/2081/images.png", "web_url": "https://gitlab.qima-inc.com/groups/fe-middle-platform"}}, {"id": 81, "description": "有赞官网", "name": "intro", "name_with_namespace": "fe / intro", "path": "intro", "path_with_namespace": "fe/intro", "created_at": "2016-01-06T06:28:22.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/intro.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/intro.git", "web_url": "https://gitlab.qima-inc.com/fe/intro", "readme_url": "https://gitlab.qima-inc.com/fe/intro/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 8, "last_activity_at": "2025-06-30T09:13:41.153Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 14911, "description": "", "name": "minishopify-sdk", "name_with_namespace": "weapp / minishopify-sdk", "path": "minishopify-sdk", "path_with_namespace": "weapp/minishopify-sdk", "created_at": "2024-02-29T08:28:41.205Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:weapp/minishopify-sdk.git", "http_url_to_repo": "https://gitlab.qima-inc.com/weapp/minishopify-sdk.git", "web_url": "https://gitlab.qima-inc.com/weapp/minishopify-sdk", "readme_url": "https://gitlab.qima-inc.com/weapp/minishopify-sdk/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-06-30T08:28:20.934Z", "namespace": {"id": 356, "name": "weapp", "path": "weapp", "kind": "group", "full_path": "weapp", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg", "web_url": "https://gitlab.qima-inc.com/groups/weapp"}}, {"id": 12780, "description": "连锁B端PC部署仓库", "name": "chain-b-pc-dist", "name_with_namespace": "wsc-node / chain-b-pc-dist", "path": "chain-b-pc-dist", "path_with_namespace": "wsc-node/chain-b-pc-dist", "created_at": "2021-09-07T07:19:06.127Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/chain-b-pc-dist.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/chain-b-pc-dist.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/chain-b-pc-dist", "readme_url": null, "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-06-30T07:52:44.592Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 6806, "description": "资产金融发布项目（不需要维护、只用户发布）", "name": "assets-fin", "name_with_namespace": "fe / assets-fin", "path": "assets-fin", "path_with_namespace": "fe/assets-fin", "created_at": "2019-03-04T02:54:21.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/assets-fin.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/assets-fin.git", "web_url": "https://gitlab.qima-inc.com/fe/assets-fin", "readme_url": "https://gitlab.qima-inc.com/fe/assets-fin/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-06-30T07:40:22.308Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 5769, "description": "高汇通平台 垃圾桶", "name": "gaohuitong-merchant", "name_with_namespace": "fe / gaohuitong-merchant", "path": "gaohuitong-merchant", "path_with_namespace": "fe/gaohuitong-merchant", "created_at": "2018-09-29T08:10:38.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/gaohuitong-merchant.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/gaohuitong-merchant.git", "web_url": "https://gitlab.qima-inc.com/fe/gaohuitong-merchant", "readme_url": "https://gitlab.qima-inc.com/fe/gaohuitong-merchant/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-06-30T07:39:18.480Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 9024, "description": "前端工程化体系 koko", "name": "koko", "name_with_namespace": "fe / koko", "path": "koko", "path_with_namespace": "fe/koko", "created_at": "2020-02-17T03:06:12.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/koko.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/koko.git", "web_url": "https://gitlab.qima-inc.com/fe/koko", "readme_url": "https://gitlab.qima-inc.com/fe/koko/-/blob/master/README.md", "avatar_url": null, "forks_count": 1, "star_count": 13, "last_activity_at": "2025-06-30T06:48:41.460Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 14711, "description": "", "name": "youzan-utils", "name_with_namespace": "fe / youzan-utils", "path": "youzan-utils", "path_with_namespace": "fe/youzan-utils", "created_at": "2023-06-25T11:23:33.347Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/youzan-utils.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/youzan-utils.git", "web_url": "https://gitlab.qima-inc.com/fe/youzan-utils", "readme_url": null, "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-06-30T03:54:17.789Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 14942, "description": "", "name": "diff-pack-core", "name_with_namespace": "weapp / diff-pack-core", "path": "diff-pack-core", "path_with_namespace": "weapp/diff-pack-core", "created_at": "2024-04-03T03:56:23.494Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:weapp/diff-pack-core.git", "http_url_to_repo": "https://gitlab.qima-inc.com/weapp/diff-pack-core.git", "web_url": "https://gitlab.qima-inc.com/weapp/diff-pack-core", "readme_url": "https://gitlab.qima-inc.com/weapp/diff-pack-core/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-06-27T09:00:33.612Z", "namespace": {"id": 356, "name": "weapp", "path": "weapp", "kind": "group", "full_path": "weapp", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg", "web_url": "https://gitlab.qima-inc.com/groups/weapp"}}, {"id": 2355, "description": "", "name": "staticRedirectRules", "name_with_namespace": "fe / staticRedirectRules", "path": "staticRedirectRules", "path_with_namespace": "fe/staticRedirectRules", "created_at": "2017-04-19T05:49:51.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/staticRedirectRules.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/staticRedirectRules.git", "web_url": "https://gitlab.qima-inc.com/fe/staticRedirectRules", "readme_url": "https://gitlab.qima-inc.com/fe/staticRedirectRules/-/blob/master/README.md", "avatar_url": null, "forks_count": 3, "star_count": 35, "last_activity_at": "2025-06-27T03:11:32.011Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 8976, "description": "电商前端内部工具平台", "name": "fe-platform", "name_with_namespace": "ebiz-web / fe-platform", "path": "fe-platform", "path_with_namespace": "ebiz-web/fe-platform", "created_at": "2020-02-04T13:14:55.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:ebiz-web/fe-platform.git", "http_url_to_repo": "https://gitlab.qima-inc.com/ebiz-web/fe-platform.git", "web_url": "https://gitlab.qima-inc.com/ebiz-web/fe-platform", "readme_url": null, "avatar_url": null, "forks_count": 0, "star_count": 1, "last_activity_at": "2025-06-26T08:19:16.997Z", "namespace": {"id": 723, "name": "ebiz-web", "path": "ebiz-web", "kind": "group", "full_path": "ebiz-web", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/723/2_a499d8d8c31983ce1b5d1e45e629e061_con.png", "web_url": "https://gitlab.qima-inc.com/groups/ebiz-web"}}, {"id": 15412, "description": "", "name": "ai-do", "name_with_namespace": "fe / ai-do", "path": "ai-do", "path_with_namespace": "fe/ai-do", "created_at": "2025-06-17T13:31:52.969Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/ai-do.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/ai-do.git", "web_url": "https://gitlab.qima-inc.com/fe/ai-do", "readme_url": null, "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-06-26T04:09:32.859Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 11371, "description": "", "name": "wsc-h5-shopcore", "name_with_namespace": "wsc-node / wsc-h5-shopcore", "path": "wsc-h5-shopcore", "path_with_namespace": "wsc-node/wsc-h5-shopcore", "created_at": "2021-01-22T09:10:02.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/wsc-h5-shopcore.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-shopcore.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-shopcore", "readme_url": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-shopcore/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-06-26T01:52:28.994Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 15456, "description": "", "name": "rpa-service", "name_with_namespace": "fe-middle-platform / rpa-service", "path": "rpa-service", "path_with_namespace": "fe-middle-platform/rpa-service", "created_at": "2025-06-25T07:38:32.336Z", "default_branch": null, "tag_list": [], "ssh_url_to_repo": "***********************:fe-middle-platform/rpa-service.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe-middle-platform/rpa-service.git", "web_url": "https://gitlab.qima-inc.com/fe-middle-platform/rpa-service", "readme_url": null, "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-06-25T07:38:32.336Z", "namespace": {"id": 2081, "name": "fe-middle-platform", "path": "fe-middle-platform", "kind": "group", "full_path": "fe-middle-platform", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/2081/images.png", "web_url": "https://gitlab.qima-inc.com/groups/fe-middle-platform"}}, {"id": 7490, "description": "资产h5端业务组件库，独立发子包", "name": "assets-h5-components", "name_with_namespace": "fe-assets / assets-h5-components", "path": "assets-h5-components", "path_with_namespace": "fe-assets/assets-h5-components", "created_at": "2019-06-04T08:00:57.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe-assets/assets-h5-components.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe-assets/assets-h5-components.git", "web_url": "https://gitlab.qima-inc.com/fe-assets/assets-h5-components", "readme_url": "https://gitlab.qima-inc.com/fe-assets/assets-h5-components/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 1, "last_activity_at": "2025-06-25T07:05:38.772Z", "namespace": {"id": 1442, "name": "fe-assets", "path": "fe-assets", "kind": "group", "full_path": "fe-assets", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/fe-assets"}}, {"id": 11624, "description": "", "name": "matrix-mono", "name_with_namespace": "fe / matrix-mono", "path": "matrix-mono", "path_with_namespace": "fe/matrix-mono", "created_at": "2021-03-08T12:16:05.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/matrix-mono.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/matrix-mono.git", "web_url": "https://gitlab.qima-inc.com/fe/matrix-mono", "readme_url": "https://gitlab.qima-inc.com/fe/matrix-mono/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 1, "last_activity_at": "2025-06-25T03:37:55.685Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 6410, "description": "", "name": "zan-proxy-plugins", "name_with_namespace": "fe / zan-proxy-plugins", "path": "zan-proxy-plugins", "path_with_namespace": "fe/zan-proxy-plugins", "created_at": "2019-01-02T07:01:35.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/zan-proxy-plugins.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/zan-proxy-plugins.git", "web_url": "https://gitlab.qima-inc.com/fe/zan-proxy-plugins", "readme_url": "https://gitlab.qima-inc.com/fe/zan-proxy-plugins/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-06-24T12:51:16.006Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 11915, "description": "店铺 - 多端中台化扩展仓库", "name": "ext-tee-shop", "name_with_namespace": "weapp / ext-tee-shop", "path": "ext-tee-shop", "path_with_namespace": "weapp/ext-tee-shop", "created_at": "2021-04-19T07:24:26.306Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:weapp/ext-tee-shop.git", "http_url_to_repo": "https://gitlab.qima-inc.com/weapp/ext-tee-shop.git", "web_url": "https://gitlab.qima-inc.com/weapp/ext-tee-shop", "readme_url": "https://gitlab.qima-inc.com/weapp/ext-tee-shop/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-06-24T09:55:02.389Z", "namespace": {"id": 356, "name": "weapp", "path": "weapp", "kind": "group", "full_path": "weapp", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg", "web_url": "https://gitlab.qima-inc.com/groups/weapp"}}, {"id": 11645, "description": "", "name": "wsc-tee-trade-common", "name_with_namespace": "weapp / wsc-tee-trade-common", "path": "wsc-tee-trade-common", "path_with_namespace": "weapp/wsc-tee-trade-common", "created_at": "2021-03-11T02:14:43.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:weapp/wsc-tee-trade-common.git", "http_url_to_repo": "https://gitlab.qima-inc.com/weapp/wsc-tee-trade-common.git", "web_url": "https://gitlab.qima-inc.com/weapp/wsc-tee-trade-common", "readme_url": "https://gitlab.qima-inc.com/weapp/wsc-tee-trade-common/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 3, "last_activity_at": "2025-06-23T08:32:52.893Z", "namespace": {"id": 356, "name": "weapp", "path": "weapp", "kind": "group", "full_path": "weapp", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg", "web_url": "https://gitlab.qima-inc.com/groups/weapp"}}, {"id": 14374, "description": "ranta-configurator v2", "name": "ranta-conf", "name_with_namespace": "fe-middle-platform / ranta-conf", "path": "ranta-conf", "path_with_namespace": "fe-middle-platform/ranta-conf", "created_at": "2022-10-17T10:41:40.123Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe-middle-platform/ranta-conf.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe-middle-platform/ranta-conf.git", "web_url": "https://gitlab.qima-inc.com/fe-middle-platform/ranta-conf", "readme_url": "https://gitlab.qima-inc.com/fe-middle-platform/ranta-conf/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-06-19T13:01:08.206Z", "namespace": {"id": 2081, "name": "fe-middle-platform", "path": "fe-middle-platform", "kind": "group", "full_path": "fe-middle-platform", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/2081/images.png", "web_url": "https://gitlab.qima-inc.com/groups/fe-middle-platform"}}, {"id": 15299, "description": "用于承载AI相关的功能基建。", "name": "Ai Infrastructure", "name_with_namespace": "wsc-node / Ai Infrastructure", "path": "ai-infrastructure", "path_with_namespace": "wsc-node/ai-infrastructure", "created_at": "2025-04-17T01:59:59.410Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/ai-infrastructure.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/ai-infrastructure.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/ai-infrastructure", "readme_url": "https://gitlab.qima-inc.com/wsc-node/ai-infrastructure/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-06-19T08:32:09.245Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 6693, "description": "B端商业化订购业务 ", "name": "wsc-pc-subscribe", "name_with_namespace": "enable-platform-frontend / wsc-pc-subscribe", "path": "wsc-pc-subscribe", "path_with_namespace": "enable-platform-frontend/wsc-pc-subscribe", "created_at": "2019-02-12T07:00:00.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:enable-platform-frontend/wsc-pc-subscribe.git", "http_url_to_repo": "https://gitlab.qima-inc.com/enable-platform-frontend/wsc-pc-subscribe.git", "web_url": "https://gitlab.qima-inc.com/enable-platform-frontend/wsc-pc-subscribe", "readme_url": "https://gitlab.qima-inc.com/enable-platform-frontend/wsc-pc-subscribe/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 4, "last_activity_at": "2025-06-19T03:27:41.776Z", "namespace": {"id": 1147, "name": "enable-platform-frontend", "path": "enable-platform-frontend", "kind": "group", "full_path": "enable-platform-frontend", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/enable-platform-frontend"}}, {"id": 4574, "description": "有赞财务系统", "name": "finance", "name_with_namespace": "fe / finance", "path": "finance", "path_with_namespace": "fe/finance", "created_at": "2018-04-17T03:22:01.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/finance.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/finance.git", "web_url": "https://gitlab.qima-inc.com/fe/finance", "readme_url": "https://gitlab.qima-inc.com/fe/finance/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-06-19T03:27:39.254Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 5692, "description": "", "name": "assets-finance", "name_with_namespace": "fe / assets-finance", "path": "assets-finance", "path_with_namespace": "fe/assets-finance", "created_at": "2018-09-19T07:00:07.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/assets-finance.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/assets-finance.git", "web_url": "https://gitlab.qima-inc.com/fe/assets-finance", "readme_url": "https://gitlab.qima-inc.com/fe/assets-finance/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-06-19T03:27:36.519Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 259, "description": "", "name": "tech", "name_with_namespace": "fe / tech", "path": "tech", "path_with_namespace": "fe/tech", "created_at": "2016-03-22T03:40:04.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/tech.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/tech.git", "web_url": "https://gitlab.qima-inc.com/fe/tech", "readme_url": "https://gitlab.qima-inc.com/fe/tech/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-06-19T03:27:35.337Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 8437, "description": "", "name": "h5-plugins", "name_with_namespace": "wsc-node / h5-plugins", "path": "h5-plugins", "path_with_namespace": "wsc-node/h5-plugins", "created_at": "2019-10-29T03:07:33.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/h5-plugins.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/h5-plugins.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/h5-plugins", "readme_url": "https://gitlab.qima-inc.com/wsc-node/h5-plugins/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 4, "last_activity_at": "2025-06-19T02:52:10.501Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 11945, "description": "企微助手交付自动化相关源码", "name": "Weass Automation Mono", "name_with_namespace": "fe / Weass Automation Mono", "path": "weass-automation-mono", "path_with_namespace": "fe/weass-automation-mono", "created_at": "2021-04-21T06:26:07.441Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/weass-automation-mono.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/weass-automation-mono.git", "web_url": "https://gitlab.qima-inc.com/fe/weass-automation-mono", "readme_url": "https://gitlab.qima-inc.com/fe/weass-automation-mono/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-06-18T06:39:22.738Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 14839, "description": "地图服务sdk", "name": "yzmap-sdk", "name_with_namespace": "xujiazheng / yzmap-sdk", "path": "yzmap-sdk", "path_with_namespace": "xujiazheng/yzmap-sdk", "created_at": "2023-10-30T12:52:46.852Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:xujiazheng/yzmap-sdk.git", "http_url_to_repo": "https://gitlab.qima-inc.com/xujiazheng/yzmap-sdk.git", "web_url": "https://gitlab.qima-inc.com/xujiazheng/yzmap-sdk", "readme_url": "https://gitlab.qima-inc.com/xujiazheng/yzmap-sdk/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-06-18T06:26:05.912Z", "namespace": {"id": 3020, "name": "xu<PERSON>azheng", "path": "xu<PERSON>azheng", "kind": "user", "full_path": "xu<PERSON>azheng", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/xujiazheng"}}, {"id": 12146, "description": "", "name": "ranta-config-node-plugin", "name_with_namespace": "fe-middle-platform / ranta-config-node-plugin", "path": "ranta-config-node-plugin", "path_with_namespace": "fe-middle-platform/ranta-config-node-plugin", "created_at": "2021-05-25T09:36:36.091Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe-middle-platform/ranta-config-node-plugin.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe-middle-platform/ranta-config-node-plugin.git", "web_url": "https://gitlab.qima-inc.com/fe-middle-platform/ranta-config-node-plugin", "readme_url": "https://gitlab.qima-inc.com/fe-middle-platform/ranta-config-node-plugin/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-06-18T06:24:56.596Z", "namespace": {"id": 2081, "name": "fe-middle-platform", "path": "fe-middle-platform", "kind": "group", "full_path": "fe-middle-platform", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/2081/images.png", "web_url": "https://gitlab.qima-inc.com/groups/fe-middle-platform"}}, {"id": 12653, "description": "中台化基础库", "name": "ranta-base-library", "name_with_namespace": "fe-middle-platform / ranta-base-library", "path": "ranta-base-library", "path_with_namespace": "fe-middle-platform/ranta-base-library", "created_at": "2021-08-16T12:15:56.440Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe-middle-platform/ranta-base-library.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe-middle-platform/ranta-base-library.git", "web_url": "https://gitlab.qima-inc.com/fe-middle-platform/ranta-base-library", "readme_url": "https://gitlab.qima-inc.com/fe-middle-platform/ranta-base-library/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-06-16T09:11:31.092Z", "namespace": {"id": 2081, "name": "fe-middle-platform", "path": "fe-middle-platform", "kind": "group", "full_path": "fe-middle-platform", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/2081/images.png", "web_url": "https://gitlab.qima-inc.com/groups/fe-middle-platform"}}, {"id": 2507, "description": "", "name": "apidoc", "name_with_namespace": "fe / apidoc", "path": "apidoc", "path_with_namespace": "fe/apidoc", "created_at": "2017-05-12T07:41:19.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/apidoc.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/apidoc.git", "web_url": "https://gitlab.qima-inc.com/fe/apidoc", "readme_url": "https://gitlab.qima-inc.com/fe/apidoc/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-06-16T02:23:21.050Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 6402, "description": "", "name": "zan-proxy-mac-client", "name_with_namespace": "fe / zan-proxy-mac-client", "path": "zan-proxy-mac-client", "path_with_namespace": "fe/zan-proxy-mac-client", "created_at": "2018-12-30T09:28:37.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/zan-proxy-mac-client.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/zan-proxy-mac-client.git", "web_url": "https://gitlab.qima-inc.com/fe/zan-proxy-mac-client", "readme_url": "https://gitlab.qima-inc.com/fe/zan-proxy-mac-client/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 2, "last_activity_at": "2025-06-16T02:10:17.969Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 11026, "description": "", "name": "data-base-demo", "name_with_namespace": "fe / data-base-demo", "path": "data-base-demo", "path_with_namespace": "fe/data-base-demo", "created_at": "2020-12-04T02:39:31.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/data-base-demo.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/data-base-demo.git", "web_url": "https://gitlab.qima-inc.com/fe/data-base-demo", "readme_url": "https://gitlab.qima-inc.com/fe/data-base-demo/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-06-14T10:45:24.104Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 14808, "description": "", "name": "Tee User Components", "name_with_namespace": "fe / Tee User Components", "path": "tee-user-components", "path_with_namespace": "fe/tee-user-components", "created_at": "2023-09-18T02:20:24.286Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/tee-user-components.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/tee-user-components.git", "web_url": "https://gitlab.qima-inc.com/fe/tee-user-components", "readme_url": "https://gitlab.qima-inc.com/fe/tee-user-components/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-06-13T08:54:39.735Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 15284, "description": "", "name": "garden-zanuse", "name_with_namespace": "fe / garden-zanuse", "path": "garden-zanuse", "path_with_namespace": "fe/garden-zanuse", "created_at": "2025-04-09T02:39:02.597Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/garden-zanuse.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/garden-zanuse.git", "web_url": "https://gitlab.qima-inc.com/fe/garden-zanuse", "readme_url": "https://gitlab.qima-inc.com/fe/garden-zanuse/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-06-11T09:55:10.371Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 10821, "description": "Inner application manage platform，内部应用管理平台", "name": "iamp", "name_with_namespace": "iamp / iamp", "path": "iamp", "path_with_namespace": "iamp/iamp", "created_at": "2020-11-03T06:51:16.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:iamp/iamp.git", "http_url_to_repo": "https://gitlab.qima-inc.com/iamp/iamp.git", "web_url": "https://gitlab.qima-inc.com/iamp/iamp", "readme_url": "https://gitlab.qima-inc.com/iamp/iamp/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 1, "last_activity_at": "2025-06-11T02:33:43.445Z", "namespace": {"id": 2306, "name": "iamp", "path": "iamp", "kind": "group", "full_path": "iamp", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/iamp"}}, {"id": 7609, "description": "http://fedoc.qima-inc.com/", "name": "fe-doc", "name_with_namespace": "fe / fe-doc", "path": "fe-doc", "path_with_namespace": "fe/fe-doc", "created_at": "2019-06-25T02:18:02.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/fe-doc.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/fe-doc.git", "web_url": "https://gitlab.qima-inc.com/fe/fe-doc", "readme_url": "https://gitlab.qima-inc.com/fe/fe-doc/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 4, "last_activity_at": "2025-06-11T02:33:42.603Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 6091, "description": "有赞财务平台 - 对外（代理商和服务商）\r\ncommercial-finance.youzan.com", "name": "finance-external", "name_with_namespace": "fe / finance-external", "path": "finance-external", "path_with_namespace": "fe/finance-external", "created_at": "2018-11-23T06:27:34.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/finance-external.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/finance-external.git", "web_url": "https://gitlab.qima-inc.com/fe/finance-external", "readme_url": "https://gitlab.qima-inc.com/fe/finance-external/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-06-11T02:33:42.031Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 15021, "description": "", "name": "garden-ai-sales", "name_with_namespace": "fe / garden-ai-sales", "path": "garden-ai-sales", "path_with_namespace": "fe/garden-ai-sales", "created_at": "2024-07-31T02:45:29.639Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/garden-ai-sales.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/garden-ai-sales.git", "web_url": "https://gitlab.qima-inc.com/fe/garden-ai-sales", "readme_url": "https://gitlab.qima-inc.com/fe/garden-ai-sales/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-06-09T13:05:08.517Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 5654, "description": "", "name": "retail-shared", "name_with_namespace": "retail-web / retail-shared", "path": "retail-shared", "path_with_namespace": "retail-web/retail-shared", "created_at": "2018-09-13T02:01:25.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:retail-web/retail-shared.git", "http_url_to_repo": "https://gitlab.qima-inc.com/retail-web/retail-shared.git", "web_url": "https://gitlab.qima-inc.com/retail-web/retail-shared", "readme_url": "https://gitlab.qima-inc.com/retail-web/retail-shared/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 1, "last_activity_at": "2025-06-09T08:47:56.856Z", "namespace": {"id": 579, "name": "retail-web", "path": "retail-web", "kind": "group", "full_path": "retail-web", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/579/box-packing-peanut-512-min.png", "web_url": "https://gitlab.qima-inc.com/groups/retail-web"}}, {"id": 7056, "description": "", "name": "youzan-security", "name_with_namespace": "wsc-node / youzan-security", "path": "youzan-security", "path_with_namespace": "wsc-node/youzan-security", "created_at": "2019-04-12T08:02:34.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/youzan-security.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/youzan-security.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/youzan-security", "readme_url": "https://gitlab.qima-inc.com/wsc-node/youzan-security/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-06-05T09:02:12.574Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 6266, "description": "有赞精赞报名系统、有赞精选生活号", "name": "wsc-pc-mars", "name_with_namespace": "wsc-node / wsc-pc-mars", "path": "wsc-pc-mars", "path_with_namespace": "wsc-node/wsc-pc-mars", "created_at": "2018-12-12T07:17:37.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/wsc-pc-mars.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-mars.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-mars", "readme_url": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-mars/-/blob/master/README.md", "avatar_url": "https://gitlab.qima-inc.com/uploads/-/system/project/avatar/6266/basicprofile.jpeg", "forks_count": 0, "star_count": 1, "last_activity_at": "2025-06-04T10:07:56.518Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 7476, "description": "三方渠道接入统一网关，像微信、微博、快手、百度、虎牙、陌陌、知乎等各三方平台", "name": "channel-gateway", "name_with_namespace": "wsc-node / channel-gateway", "path": "channel-gateway", "path_with_namespace": "wsc-node/channel-gateway", "created_at": "2019-06-03T08:30:42.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/channel-gateway.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/channel-gateway.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/channel-gateway", "readme_url": "https://gitlab.qima-inc.com/wsc-node/channel-gateway/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 1, "last_activity_at": "2025-06-04T06:55:00.859Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 6038, "description": "有赞 JSBridge 适配层（Zan Native Bridge）", "name": "ZNB", "name_with_namespace": "fe / ZNB", "path": "ZNB", "path_with_namespace": "fe/ZNB", "created_at": "2018-11-15T08:09:38.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/ZNB.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/ZNB.git", "web_url": "https://gitlab.qima-inc.com/fe/ZNB", "readme_url": "https://gitlab.qima-inc.com/fe/ZNB/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 4, "last_activity_at": "2025-06-04T02:05:44.401Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 12214, "description": "下单页储值组件多端", "name": "ext-tee-retail-prepaid", "name_with_namespace": "weapp / ext-tee-retail-prepaid", "path": "ext-tee-retail-prepaid", "path_with_namespace": "weapp/ext-tee-retail-prepaid", "created_at": "2021-06-07T03:16:53.920Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:weapp/ext-tee-retail-prepaid.git", "http_url_to_repo": "https://gitlab.qima-inc.com/weapp/ext-tee-retail-prepaid.git", "web_url": "https://gitlab.qima-inc.com/weapp/ext-tee-retail-prepaid", "readme_url": "https://gitlab.qima-inc.com/weapp/ext-tee-retail-prepaid/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 1, "last_activity_at": "2025-06-03T08:14:33.478Z", "namespace": {"id": 356, "name": "weapp", "path": "weapp", "kind": "group", "full_path": "weapp", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg", "web_url": "https://gitlab.qima-inc.com/groups/weapp"}}, {"id": 4980, "description": "", "name": "weapp-log-sdk", "name_with_namespace": "fe / weapp-log-sdk", "path": "weapp-log-sdk", "path_with_namespace": "fe/weapp-log-sdk", "created_at": "2018-06-14T11:44:06.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/weapp-log-sdk.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/weapp-log-sdk.git", "web_url": "https://gitlab.qima-inc.com/fe/weapp-log-sdk", "readme_url": "https://gitlab.qima-inc.com/fe/weapp-log-sdk/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-06-03T07:20:59.974Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 15190, "description": "AI相关的杂项内容，脚本什么的", "name": "ai_things", "name_with_namespace": "mobile / ai_things", "path": "ai_things", "path_with_namespace": "mobile/ai_things", "created_at": "2025-02-24T03:41:20.339Z", "default_branch": "main", "tag_list": [], "ssh_url_to_repo": "***********************:mobile/ai_things.git", "http_url_to_repo": "https://gitlab.qima-inc.com/mobile/ai_things.git", "web_url": "https://gitlab.qima-inc.com/mobile/ai_things", "readme_url": "https://gitlab.qima-inc.com/mobile/ai_things/-/blob/main/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-06-03T06:37:07.682Z", "namespace": {"id": 349, "name": "mobile", "path": "mobile", "kind": "group", "full_path": "mobile", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/mobile"}}, {"id": 5196, "description": "wsc-h5公用代码", "name": "wsc-fe-h5-shared", "name_with_namespace": "wsc-node / wsc-fe-h5-shared", "path": "wsc-fe-h5-shared", "path_with_namespace": "wsc-node/wsc-fe-h5-shared", "created_at": "2018-07-18T09:40:40.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/wsc-fe-h5-shared.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/wsc-fe-h5-shared.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/wsc-fe-h5-shared", "readme_url": "https://gitlab.qima-inc.com/wsc-node/wsc-fe-h5-shared/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 5, "last_activity_at": "2025-05-29T02:20:23.811Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 5883, "description": "面向消费者端 —— 前端组件化容器", "name": "wsc-h5-components", "name_with_namespace": "wsc-node / wsc-h5-components", "path": "wsc-h5-components", "path_with_namespace": "wsc-node/wsc-h5-components", "created_at": "2018-10-25T03:09:56.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/wsc-h5-components.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-components.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-components", "readme_url": "https://gitlab.qima-inc.com/wsc-node/wsc-h5-components/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 9, "last_activity_at": "2025-05-28T07:43:11.477Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}, {"id": 13238, "description": "导购前端mono仓库", "name": "guide-mono", "name_with_namespace": "fe / guide-mono", "path": "guide-mono", "path_with_namespace": "fe/guide-mono", "created_at": "2021-11-17T07:09:49.603Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/guide-mono.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/guide-mono.git", "web_url": "https://gitlab.qima-inc.com/fe/guide-mono", "readme_url": "https://gitlab.qima-inc.com/fe/guide-mono/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-05-28T02:40:07.388Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 4626, "description": "", "name": "retail-node-base", "name_with_namespace": "retail-web / retail-node-base", "path": "retail-node-base", "path_with_namespace": "retail-web/retail-node-base", "created_at": "2018-04-24T10:11:51.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:retail-web/retail-node-base.git", "http_url_to_repo": "https://gitlab.qima-inc.com/retail-web/retail-node-base.git", "web_url": "https://gitlab.qima-inc.com/retail-web/retail-node-base", "readme_url": "https://gitlab.qima-inc.com/retail-web/retail-node-base/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 4, "last_activity_at": "2025-05-27T11:34:39.033Z", "namespace": {"id": 579, "name": "retail-web", "path": "retail-web", "kind": "group", "full_path": "retail-web", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/579/box-packing-peanut-512-min.png", "web_url": "https://gitlab.qima-inc.com/groups/retail-web"}}, {"id": 3688, "description": "零售前端 React 业务组件库", "name": "retail-components", "name_with_namespace": "retail-web / retail-components", "path": "retail-components", "path_with_namespace": "retail-web/retail-components", "created_at": "2017-11-01T02:09:55.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:retail-web/retail-components.git", "http_url_to_repo": "https://gitlab.qima-inc.com/retail-web/retail-components.git", "web_url": "https://gitlab.qima-inc.com/retail-web/retail-components", "readme_url": "https://gitlab.qima-inc.com/retail-web/retail-components/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 8, "last_activity_at": "2025-05-27T03:00:22.836Z", "namespace": {"id": 579, "name": "retail-web", "path": "retail-web", "kind": "group", "full_path": "retail-web", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/579/box-packing-peanut-512-min.png", "web_url": "https://gitlab.qima-inc.com/groups/retail-web"}}, {"id": 11458, "description": "海报渲染服务新仓库", "name": "youzan-poster", "name_with_namespace": "fe / youzan-poster", "path": "youzan-poster", "path_with_namespace": "fe/youzan-poster", "created_at": "2021-02-04T03:04:10.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe/youzan-poster.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe/youzan-poster.git", "web_url": "https://gitlab.qima-inc.com/fe/youzan-poster", "readme_url": "https://gitlab.qima-inc.com/fe/youzan-poster/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-05-22T08:38:19.643Z", "namespace": {"id": 25, "name": "fe", "path": "fe", "kind": "group", "full_path": "fe", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/25/html-css-js.png", "web_url": "https://gitlab.qima-inc.com/groups/fe"}}, {"id": 6646, "description": "资产业务组件库", "name": "assets-components", "name_with_namespace": "fe-assets / assets-components", "path": "assets-components", "path_with_namespace": "fe-assets/assets-components", "created_at": "2019-01-30T02:32:20.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:fe-assets/assets-components.git", "http_url_to_repo": "https://gitlab.qima-inc.com/fe-assets/assets-components.git", "web_url": "https://gitlab.qima-inc.com/fe-assets/assets-components", "readme_url": "https://gitlab.qima-inc.com/fe-assets/assets-components/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 0, "last_activity_at": "2025-05-22T06:36:52.384Z", "namespace": {"id": 1442, "name": "fe-assets", "path": "fe-assets", "kind": "group", "full_path": "fe-assets", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/fe-assets"}}, {"id": 4260, "description": "", "name": "wsc-utils", "name_with_namespace": "weapp / wsc-utils", "path": "wsc-utils", "path_with_namespace": "weapp/wsc-utils", "created_at": "2018-02-20T08:37:01.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:weapp/wsc-utils.git", "http_url_to_repo": "https://gitlab.qima-inc.com/weapp/wsc-utils.git", "web_url": "https://gitlab.qima-inc.com/weapp/wsc-utils", "readme_url": "https://gitlab.qima-inc.com/weapp/wsc-utils/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 4, "last_activity_at": "2025-05-21T03:51:49.799Z", "namespace": {"id": 356, "name": "weapp", "path": "weapp", "kind": "group", "full_path": "weapp", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg", "web_url": "https://gitlab.qima-inc.com/groups/weapp"}}, {"id": 11928, "description": "营销 - 多端中台化扩展仓库", "name": "ext-tee-wsc-ump", "name_with_namespace": "weapp / ext-tee-wsc-ump", "path": "ext-tee-wsc-ump", "path_with_namespace": "weapp/ext-tee-wsc-ump", "created_at": "2021-04-20T06:40:33.483Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:weapp/ext-tee-wsc-ump.git", "http_url_to_repo": "https://gitlab.qima-inc.com/weapp/ext-tee-wsc-ump.git", "web_url": "https://gitlab.qima-inc.com/weapp/ext-tee-wsc-ump", "readme_url": "https://gitlab.qima-inc.com/weapp/ext-tee-wsc-ump/-/blob/master/README.md", "avatar_url": null, "forks_count": 0, "star_count": 1, "last_activity_at": "2025-05-20T09:12:57.994Z", "namespace": {"id": 356, "name": "weapp", "path": "weapp", "kind": "group", "full_path": "weapp", "parent_id": null, "avatar_url": "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg", "web_url": "https://gitlab.qima-inc.com/groups/weapp"}}, {"id": 8763, "description": "增长中心广告、CPS相关业务", "name": "wsc-pc-cps", "name_with_namespace": "wsc-node / wsc-pc-cps", "path": "wsc-pc-cps", "path_with_namespace": "wsc-node/wsc-pc-cps", "created_at": "2019-12-20T02:59:51.000Z", "default_branch": "master", "tag_list": [], "ssh_url_to_repo": "***********************:wsc-node/wsc-pc-cps.git", "http_url_to_repo": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-cps.git", "web_url": "https://gitlab.qima-inc.com/wsc-node/wsc-pc-cps", "readme_url": null, "avatar_url": null, "forks_count": 0, "star_count": 2, "last_activity_at": "2025-05-16T16:13:02.977Z", "namespace": {"id": 1065, "name": "wsc-node", "path": "wsc-node", "kind": "group", "full_path": "wsc-node", "parent_id": null, "avatar_url": null, "web_url": "https://gitlab.qima-inc.com/groups/wsc-node"}}]