import {
  getBranchs,
  getProjectInfo,
  getRepositoryFileContent,
  updateRepositoryFileContent,
} from "./api.js";

import gitProjects from "./git-projects.json";

// 获取wsc最近一次hotfix分支
export function getWscLastHotfixBranch(): Promise<any> {
  return getBranchs({ appName: "wsc", search: "^hotfix/v2.2" })
    .then((branches) =>
      branches.sort(
        (a: any, b: any) =>
          +new Date(b.commit.created_at) - +new Date(a.commit.created_at)
      )
    )
    .then((sortedBranches) => sortedBranches[0].name);
}

// 获取项目仓库地址
export function getProjectRepo(appName: any): Promise<any> {
  return getProjectInfo({
    appName,
  }).then((res) => res.ssh_url_to_repo);
}

// 获取项目id
export function getProjectId(appName: any) {
  return gitProjects.find((p: any) => p.name === appName)?.id;
}

// 获取文件内容
export function getFileContent({ appName, filePath, branch }: any) {
  return getRepositoryFileContent({
    appName,
    filePath,
    branch,
  }).then((res) => Buffer.from(res.content, "base64").toString());
}

// 更新gitlab仓库的koko.repo.json文件内容
export async function updateAppKokoRepo(options: any): Promise<any> {
  const { appName, branchName, subApps } = options;

  // 验证参数
  if (!appName || !branchName || !Array.isArray(subApps)) {
    throw new Error("appName, branchName和subApps都是必填参数");
  }

  return getFileContent({
    appName,
    filePath: "koko.repo.json",
    branch: branchName,
  }).then((content) => {
    const kokoRepo = JSON.parse(content);
    kokoRepo.repos.forEach((repo: any) => {
      if (subApps.includes(repo.name)) {
        repo.branch = branchName;
      }
    });
    return updateRepositoryFileContent({
      appName,
      filePath: "koko.repo.json",
      branch: branchName,
      content: JSON.stringify(kokoRepo, null, 2),
      commit_message: `feat: 更新${subApps}的分支为${branchName}`,
    });
  });
}

/**
 * 更新package.json文件内容
 * @param appName 仓库名称
 * @param branchName 分支名称
 * @param packageInfo 依赖信息
 * @returns 
 */
export async function updatePackageJson({
  appName,
  branchName,
  path,
  packageInfo,
}: any) {
  const filePath  = path ? `${path}/package.json` : "package.json";
  return getFileContent({
    appName,
    filePath,
    branch: branchName,
  }).then((content) => {
    const packageJson = JSON.parse(content);
    const { name, version } = packageInfo;
    if (packageJson?.dependencies?.[name]) {
      packageJson.dependencies[name] = version;
    }
    if (packageJson?.devDependencies?.[name]) {
      packageJson.devDependencies[name] = version;
    }
    if (packageJson?.peerDependencies?.[name]) {
      packageJson.peerDependencies[name] = version;
    }
    if (packageJson?.resolutions?.[name]) {
      packageJson.resolutions[name] = version;
    }
    return updateRepositoryFileContent({
      appName,
      filePath,
      branch: branchName,
      content: JSON.stringify(packageJson, null, 2),
      commit_message: `feat: 更新${name}的依赖版本为${version}`,
    });
  });
}
