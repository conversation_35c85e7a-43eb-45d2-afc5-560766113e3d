import { defineConfig } from "tsup";
import * as dotenv from "dotenv";
import path from "path";

// 加载.env文件，指定绝对路径
dotenv.config({
  path: path.resolve(__dirname, ".env"),
});

const isDev = process.env.npm_lifecycle_event === "dev";
const isDevTest = process.env.npm_lifecycle_event === "dev:test";
const packageVersion = process.env.npm_package_version;

const config: any = {
  clean: true,
  entry: ["src/index.ts", "src/cli.ts"],
  format: ["esm"],
  minify: !isDev,
  target: "esnext",
  outDir: "dist",
  outExtension: ({ format }) => ({
    js: ".js",
  }),
  onSuccess: isDev ? "node dist/cli.js" : undefined,
  define: {
    "process.env.NPM_PACKAGE_VERSION": JSON.stringify(packageVersion),
  },
};

if (isDevTest) {
  Object.assign(config, {
    entry: ["test/index.ts"],
    onSuccess: "node dist/index.js",
    define: {
      "process.env.NPM_PACKAGE_VERSION": JSON.stringify(packageVersion),
      "process.env.PORT": JSON.stringify(process.env.PORT),
      "process.env.WEBHOOK_URL": JSON.stringify(process.env.webhook_url),
      "process.env.PRIVATE_TOKEN": JSON.stringify(process.env.private_token),
    },
  });
}

export default defineConfig(config);
